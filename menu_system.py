#!/usr/bin/env python3
"""
Pokročilý menu systém pro hru Spitfire
"""

import pygame
import math
from enum import Enum
from typing import List, Callable, Optional

class MenuState(Enum):
    MAIN = "main"
    SETTINGS = "settings"
    ACHIEVEMENTS = "achievements"
    TUTORIAL = "tutorial"
    CREDITS = "credits"

class Button:
    """Pokročilé tlačítko s animacemi"""
    
    def __init__(self, x, y, width, height, text, callback=None, color=(100, 100, 100)):
        self.rect = pygame.Rect(x, y, width, height)
        self.text = text
        self.callback = callback
        self.base_color = color
        self.current_color = color
        self.hover_color = (min(255, color[0] + 50), min(255, color[1] + 50), min(255, color[2] + 50))
        self.click_color = (max(0, color[0] - 30), max(0, color[1] - 30), max(0, color[2] - 30))
        
        self.is_hovered = False
        self.is_clicked = False
        self.animation_timer = 0
        self.scale = 1.0
        self.target_scale = 1.0
        
        # Font
        self.font = pygame.font.Font(None, 36)
        self.text_surface = self.font.render(text, True, (255, 255, 255))
        self.text_rect = self.text_surface.get_rect(center=self.rect.center)
    
    def update(self, mouse_pos, mouse_clicked):
        """Aktualizuje stav tlačítka"""
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        
        if self.is_hovered:
            self.target_scale = 1.1
            if mouse_clicked:
                self.is_clicked = True
                if self.callback:
                    self.callback()
        else:
            self.target_scale = 1.0
            self.is_clicked = False
        
        # Animace škálování
        self.scale += (self.target_scale - self.scale) * 0.1
        
        # Animace barvy
        if self.is_clicked:
            target_color = self.click_color
        elif self.is_hovered:
            target_color = self.hover_color
        else:
            target_color = self.base_color
        
        self.current_color = (
            int(self.current_color[0] + (target_color[0] - self.current_color[0]) * 0.1),
            int(self.current_color[1] + (target_color[1] - self.current_color[1]) * 0.1),
            int(self.current_color[2] + (target_color[2] - self.current_color[2]) * 0.1)
        )
        
        self.animation_timer += 1
    
    def draw(self, surface):
        """Vykreslí tlačítko"""
        # Škálovaný rect
        scaled_width = int(self.rect.width * self.scale)
        scaled_height = int(self.rect.height * self.scale)
        scaled_rect = pygame.Rect(0, 0, scaled_width, scaled_height)
        scaled_rect.center = self.rect.center
        
        # Stín
        shadow_rect = scaled_rect.copy()
        shadow_rect.x += 3
        shadow_rect.y += 3
        pygame.draw.rect(surface, (50, 50, 50), shadow_rect, border_radius=10)
        
        # Hlavní tlačítko
        pygame.draw.rect(surface, self.current_color, scaled_rect, border_radius=10)
        
        # Okraj
        border_color = (255, 255, 255) if self.is_hovered else (200, 200, 200)
        pygame.draw.rect(surface, border_color, scaled_rect, 3, border_radius=10)
        
        # Text
        text_rect = self.text_surface.get_rect(center=scaled_rect.center)
        surface.blit(self.text_surface, text_rect)

class Slider:
    """Posuvník pro nastavení"""
    
    def __init__(self, x, y, width, min_val, max_val, initial_val, label):
        self.rect = pygame.Rect(x, y, width, 20)
        self.min_val = min_val
        self.max_val = max_val
        self.val = initial_val
        self.label = label
        self.dragging = False
        
        self.font = pygame.font.Font(None, 24)
        
        # Pozice posuvníku
        self.slider_pos = self.val_to_pos(self.val)
    
    def val_to_pos(self, val):
        """Převede hodnotu na pozici"""
        ratio = (val - self.min_val) / (self.max_val - self.min_val)
        return self.rect.x + ratio * self.rect.width
    
    def pos_to_val(self, pos):
        """Převede pozici na hodnotu"""
        ratio = (pos - self.rect.x) / self.rect.width
        ratio = max(0, min(1, ratio))
        return self.min_val + ratio * (self.max_val - self.min_val)
    
    def update(self, mouse_pos, mouse_pressed):
        """Aktualizuje posuvník"""
        slider_rect = pygame.Rect(self.slider_pos - 10, self.rect.y - 5, 20, 30)
        
        if mouse_pressed and slider_rect.collidepoint(mouse_pos):
            self.dragging = True
        elif not mouse_pressed:
            self.dragging = False
        
        if self.dragging:
            self.slider_pos = max(self.rect.x, min(self.rect.x + self.rect.width, mouse_pos[0]))
            self.val = self.pos_to_val(self.slider_pos)
    
    def draw(self, surface):
        """Vykreslí posuvník"""
        # Label
        label_surface = self.font.render(f"{self.label}: {self.val:.2f}", True, (255, 255, 255))
        surface.blit(label_surface, (self.rect.x, self.rect.y - 25))
        
        # Linka
        pygame.draw.line(surface, (100, 100, 100), 
                        (self.rect.x, self.rect.centery), 
                        (self.rect.x + self.rect.width, self.rect.centery), 3)
        
        # Posuvník
        slider_rect = pygame.Rect(self.slider_pos - 10, self.rect.y - 5, 20, 30)
        pygame.draw.rect(surface, (200, 200, 200), slider_rect, border_radius=5)
        pygame.draw.rect(surface, (255, 255, 255), slider_rect, 2, border_radius=5)

class MenuManager:
    """Správce menu systému"""
    
    def __init__(self, screen_width, screen_height):
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.current_menu = MenuState.MAIN
        self.transition_alpha = 0
        self.transitioning = False
        
        # Pozadí
        self.background_stars = []
        for _ in range(100):
            star = {
                'x': pygame.math.Vector2(
                    random.uniform(0, screen_width),
                    random.uniform(0, screen_height)
                ),
                'speed': random.uniform(0.5, 2.0),
                'brightness': random.uniform(0.3, 1.0),
                'size': random.randint(1, 3)
            }
            self.background_stars.append(star)
        
        self.setup_menus()
    
    def setup_menus(self):
        """Nastaví všechna menu"""
        center_x = self.screen_width // 2
        
        # Hlavní menu
        self.main_menu_buttons = [
            Button(center_x - 100, 200, 200, 50, "Nová hra", self.start_new_game),
            Button(center_x - 100, 270, 200, 50, "Pokračovat", self.continue_game),
            Button(center_x - 100, 340, 200, 50, "Nastavení", self.open_settings),
            Button(center_x - 100, 410, 200, 50, "Achievementy", self.open_achievements),
            Button(center_x - 100, 480, 200, 50, "Konec", self.quit_game)
        ]
        
        # Menu nastavení
        self.settings_sliders = [
            Slider(center_x - 150, 200, 300, 0.0, 1.0, 0.7, "Hlavní hlasitost"),
            Slider(center_x - 150, 270, 300, 0.0, 1.0, 0.5, "Hudba"),
            Slider(center_x - 150, 340, 300, 0.0, 1.0, 0.8, "Zvukové efekty")
        ]
        
        self.settings_buttons = [
            Button(center_x - 200, 450, 120, 40, "Zpět", self.back_to_main),
            Button(center_x - 60, 450, 120, 40, "Uložit", self.save_settings),
            Button(center_x + 80, 450, 120, 40, "Reset", self.reset_settings)
        ]
    
    def update(self, mouse_pos, mouse_clicked, mouse_pressed):
        """Aktualizuje menu"""
        # Animace hvězd
        for star in self.background_stars:
            star['x'].y += star['speed']
            if star['x'].y > self.screen_height:
                star['x'].y = -10
                star['x'].x = random.uniform(0, self.screen_width)
        
        # Aktualizace tlačítek podle aktuálního menu
        if self.current_menu == MenuState.MAIN:
            for button in self.main_menu_buttons:
                button.update(mouse_pos, mouse_clicked)
        
        elif self.current_menu == MenuState.SETTINGS:
            for slider in self.settings_sliders:
                slider.update(mouse_pos, mouse_pressed)
            for button in self.settings_buttons:
                button.update(mouse_pos, mouse_clicked)
    
    def draw(self, surface):
        """Vykreslí menu"""
        # Pozadí
        surface.fill((20, 20, 40))
        
        # Hvězdy
        for star in self.background_stars:
            color_intensity = int(255 * star['brightness'])
            color = (color_intensity, color_intensity, color_intensity)
            pygame.draw.circle(surface, color, 
                             (int(star['x'].x), int(star['x'].y)), star['size'])
        
        # Titul hry
        title_font = pygame.font.Font(None, 72)
        title_surface = title_font.render("SPITFIRE", True, (255, 215, 0))
        title_rect = title_surface.get_rect(center=(self.screen_width // 2, 100))
        
        # Efekt svítění titulu
        glow_surface = title_font.render("SPITFIRE", True, (255, 255, 100))
        for offset in [(2, 2), (-2, -2), (2, -2), (-2, 2)]:
            glow_rect = title_rect.copy()
            glow_rect.x += offset[0]
            glow_rect.y += offset[1]
            surface.blit(glow_surface, glow_rect)
        
        surface.blit(title_surface, title_rect)
        
        # Vykreslení podle aktuálního menu
        if self.current_menu == MenuState.MAIN:
            for button in self.main_menu_buttons:
                button.draw(surface)
        
        elif self.current_menu == MenuState.SETTINGS:
            # Nadpis
            settings_font = pygame.font.Font(None, 48)
            settings_surface = settings_font.render("NASTAVENÍ", True, (255, 255, 255))
            settings_rect = settings_surface.get_rect(center=(self.screen_width // 2, 150))
            surface.blit(settings_surface, settings_rect)
            
            # Posuvníky a tlačítka
            for slider in self.settings_sliders:
                slider.draw(surface)
            for button in self.settings_buttons:
                button.draw(surface)
    
    # Callback funkce
    def start_new_game(self):
        print("Spouštím novou hru...")
        # Zde by se spustila nová hra
    
    def continue_game(self):
        print("Pokračuji ve hře...")
        # Zde by se načetla uložená hra
    
    def open_settings(self):
        self.current_menu = MenuState.SETTINGS
    
    def open_achievements(self):
        self.current_menu = MenuState.ACHIEVEMENTS
    
    def back_to_main(self):
        self.current_menu = MenuState.MAIN
    
    def save_settings(self):
        print("Ukládám nastavení...")
        # Zde by se uložila nastavení
    
    def reset_settings(self):
        print("Resetuji nastavení...")
        # Zde by se resetovala nastavení
    
    def quit_game(self):
        print("Ukončuji hru...")
        pygame.quit()
        import sys
        sys.exit()

# Vylepšený tutorial systém
class TutorialSystem:
    """Systém pro tutorial"""
    
    def __init__(self):
        self.steps = [
            {"text": "Vítejte v hře Spitfire! Použijte šipky pro pohyb.", "highlight": None},
            {"text": "Stiskněte MEZERNÍK pro střelbu.", "highlight": "shoot"},
            {"text": "Zničte nepřátele a vyhněte se jejich střelám.", "highlight": "enemies"},
            {"text": "Sbírejte power-upy pro vylepšení.", "highlight": "powerups"},
            {"text": "Dokončete misi a přistaňte na letišti.", "highlight": "landing"}
        ]
        self.current_step = 0
        self.active = False
        self.font = pygame.font.Font(None, 32)
    
    def start(self):
        """Spustí tutorial"""
        self.active = True
        self.current_step = 0
    
    def next_step(self):
        """Přejde na další krok"""
        self.current_step += 1
        if self.current_step >= len(self.steps):
            self.active = False
    
    def draw(self, surface):
        """Vykreslí tutorial"""
        if not self.active or self.current_step >= len(self.steps):
            return
        
        step = self.steps[self.current_step]
        
        # Pozadí pro text
        text_surface = self.font.render(step["text"], True, (255, 255, 255))
        text_rect = text_surface.get_rect()
        text_rect.centerx = surface.get_width() // 2
        text_rect.y = 50
        
        # Pozadí
        bg_rect = text_rect.inflate(20, 10)
        pygame.draw.rect(surface, (0, 0, 0, 180), bg_rect)
        pygame.draw.rect(surface, (255, 255, 255), bg_rect, 2)
        
        surface.blit(text_surface, text_rect)
        
        # Indikátor kroku
        step_text = f"{self.current_step + 1}/{len(self.steps)}"
        step_surface = pygame.font.Font(None, 24).render(step_text, True, (200, 200, 200))
        step_rect = step_surface.get_rect()
        step_rect.centerx = surface.get_width() // 2
        step_rect.y = text_rect.bottom + 10
        surface.blit(step_surface, step_rect)
