# Spitfire Game

A 2D arcade-style flying shooter game where you pilot a Spitfire aircraft, destroy enemy targets, and protect your allies.

## Game Description

In Spitfire, you control a World War II era Spitfire aircraft. The game features:

- Scrolling vertical gameplay
- Multiple enemy types to destroy
- Ground targets to attack
- Allied soldiers to protect
- Power-ups to collect
- Boss battles at the end of each level
- Take-off and landing mechanics

## Controls

- Arrow keys: Move the aircraft
- Spacebar: Fire weapons
- P: Pause/Resume game
- ESC: Quit game

## Game Mechanics

1. **Mission Structure**:
   - Each level begins with takeoff from an airfield
   - The player must destroy enemies and protect allies
   - A boss appears at the end of each level
   - After defeating the boss, the player must land on an airfield to complete the level

2. **Scoring System**:
   - Destroying enemy aircraft: 50 points
   - Destroying ground targets: 75-100 points
   - Destroying boss: 500 points
   - Damaging allies: -100 points (penalty)

3. **Health and Lives**:
   - Player has 3 lives
   - Each life has a health bar
   - Health can be replenished with power-ups

## Installation

1. Ensure you have Python 3.6+ installed
2. Install Pygame:
   ```
   pip install pygame
   ```
3. Run the game:
   ```
   python spitfire_game.py
   ```

## Asset Credits

The game uses placeholder assets. In a real implementation, you would need:
- Aircraft sprites
- Background images
- Sound effects
- Music

## Future Enhancements

- Additional weapon types
- More enemy varieties
- Multiple difficulty levels
- High score system
- Controller support