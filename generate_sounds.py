#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Spitfire Game Sound Generator
----------------------------
This script generates simple placeholder sound effects for the Spitfire game.
"""

import os
import numpy as np
import wave
import struct

def create_directories():
    """Create necessary directories for assets."""
    dirs = [
        "assets",
        "assets/images",
        "assets/sounds",
        "assets/music"
    ]
    
    for directory in dirs:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")

def generate_tone(frequency, duration, volume=1.0, sample_rate=44100):
    """Generate a simple tone."""
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    tone = np.sin(frequency * t * 2 * np.pi)
    
    # Apply a simple envelope to avoid clicks
    envelope = np.ones_like(tone)
    attack = int(sample_rate * 0.01)
    release = int(sample_rate * 0.01)
    envelope[:attack] = np.linspace(0, 1, attack)
    envelope[-release:] = np.linspace(1, 0, release)
    
    return (volume * tone * envelope).astype(np.float32)

def save_wav(filename, audio_data, sample_rate=44100):
    """Save audio data as a WAV file."""
    # Convert to 16-bit PCM
    audio_data = (audio_data * 32767).astype(np.int16)
    
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_data.tobytes())
    
    print(f"Generated {filename}")

def generate_shoot_sound():
    """Generate a simple shooting sound effect."""
    # High-pitched short sound
    duration = 0.2
    audio = generate_tone(880, duration, 0.7)
    
    # Add a quick pitch drop
    t = np.linspace(0, duration, len(audio), False)
    mod = np.exp(-t * 20)
    audio = audio * mod
    
    save_wav("assets/sounds/shoot.wav", audio)

def generate_explosion_sound():
    """Generate a simple explosion sound effect."""
    duration = 0.5
    sample_rate = 44100
    
    # Start with noise
    noise = np.random.uniform(-1, 1, int(sample_rate * duration))
    
    # Apply envelope
    t = np.linspace(0, duration, len(noise), False)
    envelope = np.exp(-t * 8)
    audio = noise * envelope
    
    # Add some low frequency rumble
    rumble = generate_tone(60, duration, 0.8)
    audio = audio * 0.7 + rumble * 0.3
    
    save_wav("assets/sounds/explosion.wav", audio)

def generate_hit_sound():
    """Generate a simple hit sound effect."""
    duration = 0.15
    sample_rate = 44100
    
    # Mix of noise and tone
    noise = np.random.uniform(-1, 1, int(sample_rate * duration))
    tone = generate_tone(220, duration, 0.5)
    
    # Apply envelope
    t = np.linspace(0, duration, len(noise), False)
    envelope = np.exp(-t * 15)
    
    audio = (noise * 0.6 + tone * 0.4) * envelope
    
    save_wav("assets/sounds/hit.wav", audio)

def generate_ally_hit_sound():
    """Generate a simple ally hit sound effect."""
    duration = 0.3
    sample_rate = 44100
    
    # Two descending tones
    tone1 = generate_tone(440, duration/2, 0.7)
    tone2 = generate_tone(330, duration/2, 0.7)
    
    audio = np.concatenate([tone1, tone2])
    
    save_wav("assets/sounds/ally_hit.wav", audio)

def generate_powerup_sound():
    """Generate a simple powerup sound effect."""
    duration = 0.4
    sample_rate = 44100
    
    # Ascending tones
    tone1 = generate_tone(440, duration/3, 0.5)
    tone2 = generate_tone(660, duration/3, 0.6)
    tone3 = generate_tone(880, duration/3, 0.7)
    
    audio = np.concatenate([tone1, tone2, tone3])
    
    save_wav("assets/sounds/powerup.wav", audio)

def generate_game_music():
    """Generate a simple game music loop."""
    duration = 30.0  # 30 second loop for more variety
    sample_rate = 44100

    # Base rhythm
    beat_duration = 0.2
    beats_per_bar = 4
    num_bars = int(duration / (beat_duration * beats_per_bar))

    music = np.zeros(int(sample_rate * duration))

    # Define a more interesting melody pattern
    # Using pentatonic scale for a more "game-like" feel
    melody_notes = [
        # C5, D5, E5, G5, A5
        523, 587, 659, 784, 880,
        # C6, A5, G5, E5, D5
        1047, 880, 784, 659, 587
    ]

    # Define a rhythm pattern (1 = play note, 0 = rest)
    rhythm_pattern = [1, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 0]

    # Create a more interesting melody
    for bar in range(num_bars):
        for beat in range(beats_per_bar):
            for eighth in range(2):  # Divide each beat into eighth notes
                rhythm_idx = (bar * beats_per_bar * 2 + beat * 2 + eighth) % len(rhythm_pattern)

                if rhythm_pattern[rhythm_idx] == 1:
                    # Choose a note from the melody
                    melody_idx = (bar + beat + eighth) % len(melody_notes)
                    note = melody_notes[melody_idx]

                    # Add some variation based on the bar
                    if bar % 4 == 3:  # Every 4th bar, transpose up
                        note = note * 9/8  # Up a whole step

                    start_time = (bar * beats_per_bar + beat) * beat_duration + eighth * beat_duration/2
                    note_duration = beat_duration * 0.4  # Shorter notes for more staccato feel

                    start_sample = int(start_time * sample_rate)

                    tone = generate_tone(note, note_duration, 0.3)
                    end_sample = min(start_sample + len(tone), len(music))
                    music[start_sample:end_sample] += tone[:end_sample-start_sample]

    # Add a more interesting bass line
    bass_progression = [
        # C, G, A, F
        65.41, 98.00, 110.00, 87.31,
        # C, G, E, D
        65.41, 98.00, 82.41, 73.42
    ]

    for bar in range(num_bars):
        bass_idx = bar % len(bass_progression)
        bass_note = bass_progression[bass_idx]

        # Play on beats 1 and 3
        for beat_offset in [0, 2]:
            start_time = (bar * beats_per_bar + beat_offset) * beat_duration
            bass_duration = beat_duration * 1.5  # Slightly longer for more sustain

            start_sample = int(start_time * sample_rate)

            bass_tone = generate_tone(bass_note, bass_duration, 0.4)
            end_sample = min(start_sample + len(bass_tone), len(music))
            music[start_sample:end_sample] += bass_tone[:end_sample-start_sample]

    # Add a simple drum pattern
    drum_pattern = [1, 0, 0, 0, 1, 0, 0, 0]  # Basic kick drum on 1 and 3
    hihat_pattern = [1, 1, 1, 1, 1, 1, 1, 1]  # Hi-hat on every eighth note

    for bar in range(num_bars):
        for beat in range(beats_per_bar):
            for eighth in range(2):
                pattern_idx = (beat * 2 + eighth) % len(drum_pattern)

                # Kick drum
                if drum_pattern[pattern_idx] == 1:
                    start_time = (bar * beats_per_bar + beat) * beat_duration + eighth * beat_duration/2

                    # Create a simple kick drum sound
                    kick_duration = 0.1
                    t = np.linspace(0, kick_duration, int(sample_rate * kick_duration), False)
                    kick = np.sin(60 * t * 2 * np.pi) * np.exp(-t * 40)

                    start_sample = int(start_time * sample_rate)
                    end_sample = min(start_sample + len(kick), len(music))
                    music[start_sample:end_sample] += kick[:end_sample-start_sample] * 0.5

                # Hi-hat
                if hihat_pattern[pattern_idx] == 1:
                    start_time = (bar * beats_per_bar + beat) * beat_duration + eighth * beat_duration/2

                    # Create a simple hi-hat sound (filtered noise)
                    hihat_duration = 0.05
                    hihat_samples = int(sample_rate * hihat_duration)
                    hihat = np.random.uniform(-1, 1, hihat_samples) * np.exp(-np.linspace(0, 10, hihat_samples))

                    start_sample = int(start_time * sample_rate)
                    end_sample = min(start_sample + len(hihat), len(music))
                    music[start_sample:end_sample] += hihat[:end_sample-start_sample] * 0.2

    # Add some variation - every 8 bars, add a cymbal crash
    for bar in range(0, num_bars, 8):
        start_time = bar * beats_per_bar * beat_duration

        # Create a simple cymbal sound (longer noise with slow decay)
        cymbal_duration = 1.0
        cymbal_samples = int(sample_rate * cymbal_duration)
        cymbal = np.random.uniform(-1, 1, cymbal_samples) * np.exp(-np.linspace(0, 5, cymbal_samples))

        start_sample = int(start_time * sample_rate)
        end_sample = min(start_sample + len(cymbal), len(music))
        music[start_sample:end_sample] += cymbal[:end_sample-start_sample] * 0.3

    # Normalize to avoid clipping
    max_val = np.max(np.abs(music))
    if max_val > 0:
        music = music / max_val * 0.9

    # Save as WAV
    save_wav("assets/music/game_music.wav", music)

    # Copy the file to have both .wav and .mp3 extensions
    # This ensures the game can find the music file regardless of which extension it's looking for
    try:
        import shutil
        shutil.copy("assets/music/game_music.wav", "assets/music/game_music.mp3")
        print("Copied game_music.wav to game_music.mp3")
    except Exception as e:
        print(f"Failed to copy music file: {e}")
        # Fallback - save again with .mp3 extension
        save_wav("assets/music/game_music.mp3", music)

def main():
    """Generate all game sounds."""
    # Create necessary directories
    create_directories()
    
    # Generate sound effects
    generate_shoot_sound()
    generate_explosion_sound()
    generate_hit_sound()
    generate_ally_hit_sound()
    generate_powerup_sound()
    generate_game_music()
    
    print("Sound generation complete!")

if __name__ == "__main__":
    main()