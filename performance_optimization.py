#!/usr/bin/env python3
"""
Optimalizace výkonu pro hru Spitfire
"""

import pygame
import math
import time
from typing import List, Dict, Any, Optional
from collections import deque

class SpatialGrid:
    """Prostorová mřížka pro rychlé kolize"""
    
    def __init__(self, width, height, cell_size=64):
        self.width = width
        self.height = height
        self.cell_size = cell_size
        self.cols = math.ceil(width / cell_size)
        self.rows = math.ceil(height / cell_size)
        self.grid = {}
        self.clear()
    
    def clear(self):
        """Vyčistí mřížku"""
        self.grid = {}
        for row in range(self.rows):
            for col in range(self.cols):
                self.grid[(col, row)] = []
    
    def get_cell(self, x, y):
        """Získá buňku pro pozici"""
        col = max(0, min(self.cols - 1, int(x // self.cell_size)))
        row = max(0, min(self.rows - 1, int(y // self.cell_size)))
        return (col, row)
    
    def insert(self, obj, x, y):
        """Vloží objekt do mřížky"""
        cell = self.get_cell(x, y)
        if cell in self.grid:
            self.grid[cell].append(obj)
    
    def get_nearby_objects(self, x, y, radius=1):
        """Získá objekty v okolí"""
        center_cell = self.get_cell(x, y)
        nearby_objects = []
        
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                cell = (center_cell[0] + dx, center_cell[1] + dy)
                if cell in self.grid:
                    nearby_objects.extend(self.grid[cell])
        
        return nearby_objects

class OptimizedCollisionManager:
    """Optimalizovaný správce kolizí"""
    
    def __init__(self, screen_width, screen_height):
        self.spatial_grid = SpatialGrid(screen_width, screen_height)
        self.collision_pairs = []
    
    def update(self, sprites_dict):
        """Aktualizuje kolizní systém"""
        self.spatial_grid.clear()
        
        # Vložení všech objektů do mřížky
        for group_name, sprite_group in sprites_dict.items():
            for sprite in sprite_group:
                if hasattr(sprite, 'rect'):
                    self.spatial_grid.insert(sprite, sprite.rect.centerx, sprite.rect.centery)
    
    def check_collisions(self, group1, group2):
        """Kontroluje kolize mezi dvěma skupinami"""
        collisions = []
        
        for sprite1 in group1:
            if not hasattr(sprite1, 'rect'):
                continue
                
            nearby = self.spatial_grid.get_nearby_objects(
                sprite1.rect.centerx, sprite1.rect.centery
            )
            
            for sprite2 in nearby:
                if sprite2 in group2 and sprite1.rect.colliderect(sprite2.rect):
                    collisions.append((sprite1, sprite2))
        
        return collisions

class RenderOptimizer:
    """Optimalizátor vykreslování"""
    
    def __init__(self, screen_width, screen_height):
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.visible_rect = pygame.Rect(0, 0, screen_width, screen_height)
        self.dirty_rects = []
        self.background_cache = {}
        
    def is_visible(self, sprite):
        """Kontroluje, zda je sprite viditelný"""
        if not hasattr(sprite, 'rect'):
            return True
        return self.visible_rect.colliderect(sprite.rect)
    
    def get_visible_sprites(self, sprite_group):
        """Vrátí pouze viditelné sprity"""
        return [sprite for sprite in sprite_group if self.is_visible(sprite)]
    
    def add_dirty_rect(self, rect):
        """Přidá dirty rect pro částečné překreslení"""
        self.dirty_rects.append(rect)
    
    def optimize_background_rendering(self, background_surface, scroll_y):
        """Optimalizuje vykreslování pozadí"""
        cache_key = int(scroll_y // 32)  # Cache každých 32 pixelů
        
        if cache_key not in self.background_cache:
            # Vytvoří nový cached background
            cached_bg = pygame.Surface((self.screen_width, self.screen_height))
            # Zde by bylo vykreslení pozadí
            self.background_cache[cache_key] = cached_bg
            
            # Omezení velikosti cache
            if len(self.background_cache) > 10:
                oldest_key = min(self.background_cache.keys())
                del self.background_cache[oldest_key]
        
        return self.background_cache[cache_key]

class FrameRateManager:
    """Správce frame rate a adaptivní kvality"""
    
    def __init__(self, target_fps=60):
        self.target_fps = target_fps
        self.target_frame_time = 1000.0 / target_fps
        self.frame_times = deque(maxlen=60)
        self.quality_level = 1.0  # 0.0 - 1.0
        self.last_adjustment = 0
        
    def update(self, frame_time_ms):
        """Aktualizuje frame rate manager"""
        self.frame_times.append(frame_time_ms)
        
        # Úprava kvality každých 60 framů
        if len(self.frame_times) >= 60:
            avg_frame_time = sum(self.frame_times) / len(self.frame_times)
            
            current_time = time.time()
            if current_time - self.last_adjustment > 1.0:  # Každou sekundu
                self.adjust_quality(avg_frame_time)
                self.last_adjustment = current_time
    
    def adjust_quality(self, avg_frame_time):
        """Upraví kvalitu podle výkonu"""
        if avg_frame_time > self.target_frame_time * 1.2:  # 20% pomalejší
            self.quality_level = max(0.3, self.quality_level - 0.1)
            print(f"Snižuji kvalitu na {self.quality_level:.1f}")
        elif avg_frame_time < self.target_frame_time * 0.8:  # 20% rychlejší
            self.quality_level = min(1.0, self.quality_level + 0.05)
            print(f"Zvyšuji kvalitu na {self.quality_level:.1f}")
    
    def should_render_effect(self, effect_priority=0.5):
        """Rozhoduje, zda vykreslit efekt podle kvality"""
        return self.quality_level >= effect_priority

class MemoryManager:
    """Správce paměti"""
    
    def __init__(self):
        self.texture_cache = {}
        self.sound_cache = {}
        self.max_cache_size = 100
        self.cache_access_count = {}
    
    def get_texture(self, filename, size=None):
        """Získá texturu z cache nebo ji načte"""
        cache_key = f"{filename}_{size}"
        
        if cache_key in self.texture_cache:
            self.cache_access_count[cache_key] = self.cache_access_count.get(cache_key, 0) + 1
            return self.texture_cache[cache_key]
        
        # Načtení textury
        try:
            texture = pygame.image.load(filename)
            if size:
                texture = pygame.transform.scale(texture, size)
            
            # Přidání do cache
            self.texture_cache[cache_key] = texture
            self.cache_access_count[cache_key] = 1
            
            # Vyčištění cache, pokud je příliš velká
            self.cleanup_cache()
            
            return texture
        except:
            # Fallback texture
            fallback = pygame.Surface((32, 32))
            fallback.fill((255, 0, 255))  # Magenta
            return fallback
    
    def cleanup_cache(self):
        """Vyčistí cache od nejméně používaných položek"""
        if len(self.texture_cache) > self.max_cache_size:
            # Seřadí podle počtu přístupů
            sorted_items = sorted(self.cache_access_count.items(), key=lambda x: x[1])
            
            # Odstraní nejméně používané
            items_to_remove = len(self.texture_cache) - self.max_cache_size + 10
            for i in range(items_to_remove):
                if i < len(sorted_items):
                    key_to_remove = sorted_items[i][0]
                    if key_to_remove in self.texture_cache:
                        del self.texture_cache[key_to_remove]
                        del self.cache_access_count[key_to_remove]

class OptimizedParticleSystem:
    """Optimalizovaný systém částic"""
    
    def __init__(self, max_particles=1000):
        self.max_particles = max_particles
        self.particles = []
        self.particle_pool = []
        
        # Pre-allocate particles
        for _ in range(max_particles):
            particle = {
                'active': False,
                'x': 0.0, 'y': 0.0,
                'vx': 0.0, 'vy': 0.0,
                'life': 0, 'max_life': 60,
                'color': (255, 255, 255),
                'size': 2
            }
            self.particle_pool.append(particle)
    
    def emit_particle(self, x, y, vx, vy, life, color, size):
        """Emituje částici"""
        # Najde neaktivní částici
        for particle in self.particle_pool:
            if not particle['active']:
                particle['active'] = True
                particle['x'] = x
                particle['y'] = y
                particle['vx'] = vx
                particle['vy'] = vy
                particle['life'] = life
                particle['max_life'] = life
                particle['color'] = color
                particle['size'] = size
                return
    
    def update(self):
        """Aktualizuje všechny částice"""
        for particle in self.particle_pool:
            if particle['active']:
                particle['x'] += particle['vx']
                particle['y'] += particle['vy']
                particle['life'] -= 1
                
                if particle['life'] <= 0:
                    particle['active'] = False
    
    def draw(self, surface, quality_level=1.0):
        """Vykreslí částice s ohledem na kvalitu"""
        particles_to_draw = int(len([p for p in self.particle_pool if p['active']]) * quality_level)
        drawn = 0
        
        for particle in self.particle_pool:
            if particle['active'] and drawn < particles_to_draw:
                alpha = particle['life'] / particle['max_life']
                size = max(1, int(particle['size'] * alpha))
                
                pygame.draw.circle(surface, particle['color'],
                                 (int(particle['x']), int(particle['y'])), size)
                drawn += 1

class GameOptimizer:
    """Hlavní optimalizátor hry"""
    
    def __init__(self, screen_width, screen_height):
        self.collision_manager = OptimizedCollisionManager(screen_width, screen_height)
        self.render_optimizer = RenderOptimizer(screen_width, screen_height)
        self.frame_rate_manager = FrameRateManager()
        self.memory_manager = MemoryManager()
        self.particle_system = OptimizedParticleSystem()
        
        self.performance_stats = {
            'fps': 0,
            'frame_time': 0,
            'sprites_rendered': 0,
            'particles_active': 0,
            'memory_usage': 0
        }
    
    def update(self, sprites_dict, frame_time_ms):
        """Aktualizuje všechny optimalizátory"""
        self.collision_manager.update(sprites_dict)
        self.frame_rate_manager.update(frame_time_ms)
        self.particle_system.update()
        
        # Aktualizace statistik
        self.performance_stats['frame_time'] = frame_time_ms
        self.performance_stats['fps'] = 1000.0 / max(frame_time_ms, 1)
        self.performance_stats['particles_active'] = len([p for p in self.particle_system.particle_pool if p['active']])
    
    def get_quality_level(self):
        """Vrátí aktuální úroveň kvality"""
        return self.frame_rate_manager.quality_level
    
    def should_render_effect(self, priority=0.5):
        """Rozhoduje o vykreslení efektu"""
        return self.frame_rate_manager.should_render_effect(priority)
    
    def get_performance_info(self):
        """Vrátí informace o výkonu"""
        return self.performance_stats.copy()

# Utility funkce pro optimalizaci
def fast_distance(pos1, pos2):
    """Rychlý výpočet vzdálenosti (bez sqrt)"""
    dx = pos1[0] - pos2[0]
    dy = pos1[1] - pos2[1]
    return dx * dx + dy * dy

def fast_normalize(vector):
    """Rychlá normalizace vektoru"""
    length_sq = vector[0] * vector[0] + vector[1] * vector[1]
    if length_sq > 0:
        inv_length = 1.0 / math.sqrt(length_sq)
        return (vector[0] * inv_length, vector[1] * inv_length)
    return (0, 0)

def lerp(a, b, t):
    """Lineární interpolace"""
    return a + (b - a) * t

def clamp(value, min_val, max_val):
    """Omezí hodnotu na rozsah"""
    return max(min_val, min(max_val, value))
