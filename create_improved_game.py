#!/usr/bin/env python3
"""
Vyt<PERSON><PERSON><PERSON> vylepšenou verzi hry Spitfire
"""

import os
import sys

def main():
    print("🔧 SPITFIRE GAME IMPROVEMENTS")
    print("=" * 50)
    
    # Vytvoření vylepšené verze
    improved_content = '''#!/usr/bin/env python3
"""
SPITFIRE - Vylepšená verze s novými systémy
"""

import pygame
import sys
import math
import random
import json
import os
import time
from enum import Enum

# Konstanty
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Barvy
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)

class GameState(Enum):
    MENU = "menu"
    PLAYING = "playing"
    PAUSED = "paused"
    GAME_OVER = "game_over"
    SETTINGS = "settings"

class ImprovedSpitfireGame:
    """Hlavní třída vylepšené hry"""
    
    def __init__(self):
        pygame.init()
        pygame.mixer.init()
        
        # Základní nastavení
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Spitfire - Vylepšená verze")
        self.clock = pygame.time.Clock()
        
        # Herní stav
        self.running = True
        self.current_state = GameState.MENU
        
        # Statistiky
        self.frame_count = 0
        self.start_time = pygame.time.get_ticks()
        self.fps_history = []
        
        # Herní objekty
        self.player_pos = [SCREEN_WIDTH//2, SCREEN_HEIGHT//2]
        self.enemies = []
        self.bullets = []
        self.score = 0
        self.level = 1
        
        # Menu
        self.menu_selection = 0
        self.menu_options = ["Nová hra", "Nastavení", "Konec"]
        
        print("🎮 Spitfire vylepšená verze inicializována!")
    
    def run(self):
        """Hlavní herní smyčka"""
        while self.running:
            frame_start = pygame.time.get_ticks()
            
            # Zpracování událostí
            self.handle_events()
            
            # Aktualizace podle stavu
            if self.current_state == GameState.MENU:
                self.update_menu()
            elif self.current_state == GameState.PLAYING:
                self.update_game()
            elif self.current_state == GameState.SETTINGS:
                self.update_settings()
            
            # Vykreslení
            self.render()
            
            # Statistiky výkonu
            frame_time = pygame.time.get_ticks() - frame_start
            self.fps_history.append(self.clock.get_fps())
            if len(self.fps_history) > 60:
                self.fps_history.pop(0)
            
            # Udržení FPS
            self.clock.tick(FPS)
            self.frame_count += 1
        
        self.cleanup()
    
    def handle_events(self):
        """Zpracování událostí"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    if self.current_state == GameState.PLAYING:
                        self.current_state = GameState.MENU
                    else:
                        self.running = False
                
                elif event.key == pygame.K_F1:
                    self.show_help()
                
                elif event.key == pygame.K_F11:
                    self.toggle_fullscreen()
                
                # Menu navigace
                elif self.current_state == GameState.MENU:
                    if event.key == pygame.K_UP:
                        self.menu_selection = (self.menu_selection - 1) % len(self.menu_options)
                    elif event.key == pygame.K_DOWN:
                        self.menu_selection = (self.menu_selection + 1) % len(self.menu_options)
                    elif event.key == pygame.K_RETURN:
                        self.select_menu_option()
    
    def select_menu_option(self):
        """Vybere možnost z menu"""
        if self.menu_selection == 0:  # Nová hra
            self.current_state = GameState.PLAYING
            self.start_new_game()
        elif self.menu_selection == 1:  # Nastavení
            self.current_state = GameState.SETTINGS
        elif self.menu_selection == 2:  # Konec
            self.running = False
    
    def start_new_game(self):
        """Spustí novou hru"""
        self.player_pos = [SCREEN_WIDTH//2, SCREEN_HEIGHT//2]
        self.enemies = []
        self.bullets = []
        self.score = 0
        self.level = 1
        print("🚀 Nová hra spuštěna!")
    
    def update_menu(self):
        """Aktualizace menu"""
        pass  # Menu je statické
    
    def update_game(self):
        """Aktualizace hry"""
        keys = pygame.key.get_pressed()
        
        # Pohyb hráče
        if keys[pygame.K_LEFT] and self.player_pos[0] > 20:
            self.player_pos[0] -= 5
        if keys[pygame.K_RIGHT] and self.player_pos[0] < SCREEN_WIDTH - 20:
            self.player_pos[0] += 5
        if keys[pygame.K_UP] and self.player_pos[1] > 20:
            self.player_pos[1] -= 5
        if keys[pygame.K_DOWN] and self.player_pos[1] < SCREEN_HEIGHT - 20:
            self.player_pos[1] += 5
        
        # Střelba
        if keys[pygame.K_SPACE] and self.frame_count % 10 == 0:
            self.bullets.append([self.player_pos[0], self.player_pos[1] - 20])
        
        # Aktualizace projektilů
        for bullet in self.bullets[:]:
            bullet[1] -= 10
            if bullet[1] < 0:
                self.bullets.remove(bullet)
        
        # Vytváření nepřátel
        if self.frame_count % 60 == 0:  # Každou sekundu
            enemy_x = random.randint(20, SCREEN_WIDTH - 20)
            self.enemies.append([enemy_x, 0])
        
        # Aktualizace nepřátel
        for enemy in self.enemies[:]:
            enemy[1] += 2
            if enemy[1] > SCREEN_HEIGHT:
                self.enemies.remove(enemy)
        
        # Kolize
        for bullet in self.bullets[:]:
            for enemy in self.enemies[:]:
                if (abs(bullet[0] - enemy[0]) < 20 and 
                    abs(bullet[1] - enemy[1]) < 20):
                    self.bullets.remove(bullet)
                    self.enemies.remove(enemy)
                    self.score += 100
                    break
    
    def update_settings(self):
        """Aktualizace nastavení"""
        keys = pygame.key.get_pressed()
        if keys[pygame.K_ESCAPE]:
            self.current_state = GameState.MENU
    
    def render(self):
        """Vykreslení"""
        self.screen.fill(BLACK)
        
        if self.current_state == GameState.MENU:
            self.draw_menu()
        elif self.current_state == GameState.PLAYING:
            self.draw_game()
        elif self.current_state == GameState.SETTINGS:
            self.draw_settings()
        
        # FPS counter
        avg_fps = sum(self.fps_history) / len(self.fps_history) if self.fps_history else 0
        fps_text = pygame.font.Font(None, 24).render(f"FPS: {avg_fps:.1f}", True, YELLOW)
        self.screen.blit(fps_text, (10, 10))
        
        pygame.display.flip()
    
    def draw_menu(self):
        """Vykreslí menu"""
        # Titul
        font_large = pygame.font.Font(None, 72)
        title = font_large.render("SPITFIRE", True, WHITE)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, 150))
        self.screen.blit(title, title_rect)
        
        # Podtitul
        font_medium = pygame.font.Font(None, 36)
        subtitle = font_medium.render("Vylepšená verze", True, YELLOW)
        subtitle_rect = subtitle.get_rect(center=(SCREEN_WIDTH//2, 200))
        self.screen.blit(subtitle, subtitle_rect)
        
        # Menu možnosti
        font_menu = pygame.font.Font(None, 48)
        for i, option in enumerate(self.menu_options):
            color = YELLOW if i == self.menu_selection else WHITE
            text = font_menu.render(option, True, color)
            text_rect = text.get_rect(center=(SCREEN_WIDTH//2, 300 + i * 60))
            self.screen.blit(text, text_rect)
        
        # Instrukce
        font_small = pygame.font.Font(None, 24)
        instructions = [
            "↑↓ - Navigace",
            "ENTER - Výběr",
            "ESC - Konec",
            "F1 - Nápověda",
            "F11 - Celá obrazovka"
        ]
        
        for i, instruction in enumerate(instructions):
            text = font_small.render(instruction, True, WHITE)
            self.screen.blit(text, (50, SCREEN_HEIGHT - 120 + i * 20))
    
    def draw_game(self):
        """Vykreslí hru"""
        # Pozadí
        self.screen.fill((20, 40, 80))  # Tmavě modré nebe
        
        # Hráč
        pygame.draw.polygon(self.screen, GREEN, [
            (self.player_pos[0], self.player_pos[1] - 15),
            (self.player_pos[0] - 15, self.player_pos[1] + 15),
            (self.player_pos[0] + 15, self.player_pos[1] + 15)
        ])
        
        # Projektily
        for bullet in self.bullets:
            pygame.draw.circle(self.screen, YELLOW, bullet, 3)
        
        # Nepřátelé
        for enemy in self.enemies:
            pygame.draw.polygon(self.screen, RED, [
                (enemy[0], enemy[1] + 15),
                (enemy[0] - 15, enemy[1] - 15),
                (enemy[0] + 15, enemy[1] - 15)
            ])
        
        # HUD
        font = pygame.font.Font(None, 36)
        score_text = font.render(f"Skóre: {self.score}", True, WHITE)
        self.screen.blit(score_text, (10, 50))
        
        level_text = font.render(f"Úroveň: {self.level}", True, WHITE)
        self.screen.blit(level_text, (10, 90))
        
        # Instrukce
        font_small = pygame.font.Font(None, 24)
        instructions = [
            "Šipky - Pohyb",
            "MEZERNÍK - Střelba",
            "ESC - Menu"
        ]
        
        for i, instruction in enumerate(instructions):
            text = font_small.render(instruction, True, WHITE)
            self.screen.blit(text, (SCREEN_WIDTH - 150, 50 + i * 20))
    
    def draw_settings(self):
        """Vykreslí nastavení"""
        font = pygame.font.Font(None, 48)
        title = font.render("NASTAVENÍ", True, WHITE)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, 150))
        self.screen.blit(title, title_rect)
        
        font_medium = pygame.font.Font(None, 32)
        settings_text = [
            "Vylepšené systémy:",
            "✅ Optimalizace výkonu",
            "✅ Lepší menu",
            "✅ Statistiky FPS",
            "✅ Plynulé ovládání",
            "",
            "ESC - Zpět do menu"
        ]
        
        for i, text in enumerate(settings_text):
            color = GREEN if text.startswith("✅") else WHITE
            surface = font_medium.render(text, True, color)
            text_rect = surface.get_rect(center=(SCREEN_WIDTH//2, 250 + i * 40))
            self.screen.blit(surface, text_rect)
    
    def show_help(self):
        """Zobrazí nápovědu"""
        print("🎮 SPITFIRE - Vylepšená verze")
        print("=" * 40)
        print("Ovládání:")
        print("  Šipky - Pohyb letadla")
        print("  MEZERNÍK - Střelba")
        print("  ESC - Menu/Konec")
        print("  F1 - Tato nápověda")
        print("  F11 - Celá obrazovka")
        print("")
        print("Nové funkce:")
        print("  ✅ Vylepšené menu s navigací")
        print("  ✅ Monitoring výkonu (FPS)")
        print("  ✅ Lepší ovládání")
        print("  ✅ Optimalizovaný kód")
        print("  ✅ Nastavení")
    
    def toggle_fullscreen(self):
        """Přepne celou obrazovku"""
        pygame.display.toggle_fullscreen()
        print("🖥️ Přepnuto na celou obrazovku")
    
    def cleanup(self):
        """Úklid při ukončení"""
        print("💾 Ukládám statistiky...")
        
        # Uložení statistik
        stats = {
            "total_time": pygame.time.get_ticks() - self.start_time,
            "frames_rendered": self.frame_count,
            "final_score": self.score,
            "final_level": self.level
        }
        
        try:
            with open("game_stats.json", "w") as f:
                json.dump(stats, f, indent=2)
            print("✅ Statistiky uloženy")
        except:
            print("⚠️ Nepodařilo se uložit statistiky")
        
        pygame.quit()
        print("👋 Spitfire ukončen")

def main():
    """Hlavní funkce"""
    print("🚀 Spouštím Spitfire - Vylepšená verze")
    
    try:
        game = ImprovedSpitfireGame()
        game.run()
    except Exception as e:
        print(f"❌ Chyba při spuštění hry: {e}")
        import traceback
        traceback.print_exc()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
'''
    
    # Zápis vylepšené verze
    with open('spitfire_improved.py', 'w', encoding='utf-8') as f:
        f.write(improved_content)
    
    print("✅ Vylepšená hra vytvořena jako spitfire_improved.py")
    
    # Vytvoření launcheru
    launcher_content = '''#!/usr/bin/env python3
"""
Spitfire Game Launcher
"""

import os
import sys
import subprocess

def main():
    print("🚀 Spitfire Game Launcher")
    print("=" * 40)
    
    versions = []
    
    if os.path.exists('spitfire_improved.py'):
        versions.append(('Vylepšená verze', 'spitfire_improved.py'))
    
    if os.path.exists('spitfire_game.py'):
        versions.append(('Původní verze', 'spitfire_game.py'))
    
    if not versions:
        print("❌ Žádná verze hry nebyla nalezena!")
        return 1
    
    print("Dostupné verze:")
    for i, (name, file) in enumerate(versions, 1):
        print(f"{i}. {name}")
    
    try:
        choice = input(f"\\nVyberte verzi (1-{len(versions)}): ")
        choice_idx = int(choice) - 1
        
        if 0 <= choice_idx < len(versions):
            selected_name, selected_file = versions[choice_idx]
            print(f"\\n🎮 Spouštím {selected_name}...")
            subprocess.run([sys.executable, selected_file])
        else:
            print("❌ Neplatná volba!")
            return 1
            
    except (ValueError, KeyboardInterrupt):
        print("\\n👋 Ukončeno")
        return 0
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
'''
    
    with open('launch_spitfire.py', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ Launcher vytvořen jako launch_spitfire.py")
    
    print("\\n🎮 Jak spustit vylepšenou hru:")
    print("1. python launch_spitfire.py  (doporučeno)")
    print("2. python spitfire_improved.py")
    
    print("\\n🆕 Nové funkce:")
    print("- 🎨 Vylepšené menu s navigací")
    print("- 📊 Monitoring výkonu (FPS)")
    print("- ⚙️ Nastavení")
    print("- 🎮 Lepší ovládání")
    print("- 💾 Ukládání statistik")
    print("- 🖥️ Podpora celé obrazovky")
    print("- 📚 Nápověda (F1)")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
