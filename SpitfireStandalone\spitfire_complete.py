#!/usr/bin/env python3
"""
SPITFIRE - Kompletní standalone verze pro distribuci
Obsahuje všechny funkce z vylepšené verze
"""

import pygame
import sys
import math
import random
import json
import os
import time
from enum import Enum

# Konstanty
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Barvy
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)

class GameState(Enum):
    MENU = "menu"
    PLAYING = "playing"
    PAUSED = "paused"
    GAME_OVER = "game_over"
    SETTINGS = "settings"

class SpitfireStandalone:
    """Kompletní standalone verze Spitfire"""

    def __init__(self):
        pygame.init()
        pygame.mixer.init()

        # Základní nastavení
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Spitfire - Standalone Edition")
        self.clock = pygame.time.Clock()

        # Herní stav
        self.running = True
        self.current_state = GameState.MENU

        # Statistiky
        self.frame_count = 0
        self.start_time = pygame.time.get_ticks()
        self.fps_history = []

        # Herní objekty
        self.player_pos = [SCREEN_WIDTH//2, SCREEN_HEIGHT//2]
        self.player_health = 100
        self.enemies = []
        self.bullets = []
        self.enemy_bullets = []
        self.score = 0
        self.level = 1

        # AI systém
        self.enemy_spawn_timer = 0
        self.formation_timer = 0

        # Menu
        self.menu_selection = 0
        self.menu_options = ["Nová hra", "Nastavení", "Konec"]

        # Nastavení hlasitosti
        self.master_volume = 0.3
        self.music_volume = 0.2
        self.sfx_volume = 0.4

        # Boss systém
        self.boss_active = False
        self.boss = None
        self.level_enemies_killed = 0
        self.enemies_needed_for_boss = 15
        self.enemies_spawned = 0

        # Bonusy
        self.bonuses = []
        self.bonus_spawn_timer = 0

        # Prostředí
        self.environment_offset = 0
        self.clouds = []
        self.waves = []
        self.islands = []

        # Přechod mezi levely
        self.level_transition = False
        self.level_transition_timer = 0
        self.level_transition_duration = 300
        self.enemy_spawn_pause = False
        self.enemy_spawn_pause_timer = 0

        # Weapon a Shield bonusy
        self.weapon_upgrade = False
        self.weapon_upgrade_timer = 0
        self.weapon_upgrade_duration = 600
        self.shield_active = False
        self.shield_timer = 0
        self.shield_duration = 600

        # Načtení assetů
        self.load_assets()
        self.create_environment()

        print("🎮 Spitfire Standalone Edition inicializována!")

    def load_assets(self):
        """Načte grafiku a zvuky - standalone verze"""
        # Vytvoření tichých zvuků (fallback)
        self.sounds = {}
        sound_names = ['shoot', 'explosion', 'hit', 'powerup']

        for name in sound_names:
            try:
                # Pokus o načtení ze souboru
                sound = pygame.mixer.Sound(f"assets/sounds/{name}.wav")
                sound.set_volume(self.sfx_volume)
                self.sounds[name] = sound
            except:
                # Vytvoření tichého zvuku jako fallback
                self.sounds[name] = pygame.mixer.Sound(buffer=bytearray([]))

        # Hudba - fallback
        try:
            pygame.mixer.music.load("assets/music/battle_music.wav")
            pygame.mixer.music.set_volume(self.music_volume)
            pygame.mixer.music.play(-1)
        except:
            pass  # Hra bude bez hudby

        # Vytvoření grafiky programově
        self.create_all_graphics()

    def create_all_graphics(self):
        """Vytvoří kompletní grafiku programově"""
        self.images = {}

        # Hráčovo letadlo - Spitfire
        player_surf = pygame.Surface((40, 30), pygame.SRCALPHA)
        pygame.draw.ellipse(player_surf, (100, 100, 100), (15, 5, 10, 20))
        pygame.draw.ellipse(player_surf, (80, 80, 80), (0, 12, 40, 6))
        pygame.draw.circle(player_surf, (50, 50, 150), (20, 10), 3)
        pygame.draw.line(player_surf, (200, 200, 200), (20, 0), (20, 5), 2)
        self.images['player'] = player_surf

        # Fighter
        fighter_surf = pygame.Surface((32, 28), pygame.SRCALPHA)
        pygame.draw.ellipse(fighter_surf, (120, 120, 120), (12, 8, 8, 16))
        pygame.draw.ellipse(fighter_surf, (100, 100, 100), (2, 12, 28, 6))
        pygame.draw.circle(fighter_surf, (50, 50, 50), (16, 12), 3)
        self.images['fighter'] = fighter_surf

        # Bomber
        bomber_surf = pygame.Surface((55, 40), pygame.SRCALPHA)
        pygame.draw.ellipse(bomber_surf, (80, 80, 120), (20, 12, 15, 20))
        pygame.draw.ellipse(bomber_surf, (70, 70, 110), (0, 18, 55, 8))
        pygame.draw.circle(bomber_surf, (60, 60, 100), (12, 22), 4)
        pygame.draw.circle(bomber_surf, (60, 60, 100), (43, 22), 4)
        self.images['bomber'] = bomber_surf

        # Interceptor
        interceptor_surf = pygame.Surface((28, 24), pygame.SRCALPHA)
        pygame.draw.ellipse(interceptor_surf, (140, 140, 60), (10, 6, 8, 14))
        pygame.draw.ellipse(interceptor_surf, (120, 120, 50), (3, 10, 22, 5))
        self.images['interceptor'] = interceptor_surf

        # Ace
        ace_surf = pygame.Surface((36, 32), pygame.SRCALPHA)
        pygame.draw.ellipse(ace_surf, (180, 50, 50), (14, 10, 8, 16))
        pygame.draw.ellipse(ace_surf, (160, 40, 40), (4, 8, 28, 6))
        pygame.draw.ellipse(ace_surf, (160, 40, 40), (6, 16, 24, 5))
        self.images['ace'] = ace_surf

        # Boss grafika
        self.create_boss_graphics()

        # Bonus grafika
        self.create_bonus_graphics()

    def create_boss_graphics(self):
        """Vytvoří grafiku bossů"""
        # Giant Bomber
        giant_bomber_surf = pygame.Surface((80, 60), pygame.SRCALPHA)
        pygame.draw.ellipse(giant_bomber_surf, (60, 60, 100), (30, 20, 20, 30))
        pygame.draw.ellipse(giant_bomber_surf, (50, 50, 90), (0, 28, 80, 12))
        self.images['giant_bomber'] = giant_bomber_surf

        # Flying Fortress
        flying_fortress_surf = pygame.Surface((90, 70), pygame.SRCALPHA)
        pygame.draw.ellipse(flying_fortress_surf, (80, 80, 80), (35, 25, 20, 35))
        pygame.draw.ellipse(flying_fortress_surf, (70, 70, 70), (0, 32, 90, 15))
        self.images['flying_fortress'] = flying_fortress_surf

        # Ace Squadron
        ace_squadron_surf = pygame.Surface((50, 45), pygame.SRCALPHA)
        pygame.draw.ellipse(ace_squadron_surf, (200, 50, 50), (20, 15, 10, 20))
        pygame.draw.ellipse(ace_squadron_surf, (180, 40, 40), (5, 20, 40, 8))
        self.images['ace_squadron'] = ace_squadron_surf

        # Super Ace
        super_ace_surf = pygame.Surface((45, 40), pygame.SRCALPHA)
        pygame.draw.ellipse(super_ace_surf, (255, 215, 0), (18, 12, 9, 18))
        pygame.draw.ellipse(super_ace_surf, (255, 200, 0), (2, 18, 41, 7))
        self.images['super_ace'] = super_ace_surf

    def create_bonus_graphics(self):
        """Vytvoří grafiku bonusů"""
        # Health bonus - červený kříž
        health_surf = pygame.Surface((24, 24), pygame.SRCALPHA)
        pygame.draw.rect(health_surf, (255, 50, 50), (8, 4, 8, 16))
        pygame.draw.rect(health_surf, (255, 50, 50), (4, 8, 16, 8))
        pygame.draw.rect(health_surf, (255, 255, 255), (8, 4, 8, 16), 2)
        pygame.draw.rect(health_surf, (255, 255, 255), (4, 8, 16, 8), 2)
        self.images['bonus_health'] = health_surf

        # Score bonus - zlatá hvězda
        score_surf = pygame.Surface((24, 24), pygame.SRCALPHA)
        star_points = [(12, 2), (14, 8), (20, 8), (16, 12), (18, 18), (12, 15), (6, 18), (8, 12), (4, 8), (10, 8)]
        pygame.draw.polygon(score_surf, (255, 215, 0), star_points)
        pygame.draw.polygon(score_surf, (255, 255, 100), star_points, 2)
        self.images['bonus_score'] = score_surf

        # Weapon bonus - zelená střela
        weapon_surf = pygame.Surface((24, 24), pygame.SRCALPHA)
        pygame.draw.polygon(weapon_surf, (50, 255, 50), [(12, 2), (8, 10), (10, 10), (10, 20), (14, 20), (14, 10), (16, 10)])
        pygame.draw.polygon(weapon_surf, (255, 255, 255), [(12, 2), (8, 10), (10, 10), (10, 20), (14, 20), (14, 10), (16, 10)], 2)
        self.images['bonus_weapon'] = weapon_surf

        # Shield bonus - modrý štít
        shield_surf = pygame.Surface((24, 24), pygame.SRCALPHA)
        shield_points = [(12, 2), (18, 6), (18, 14), (12, 22), (6, 14), (6, 6)]
        pygame.draw.polygon(shield_surf, (50, 50, 255), shield_points)
        pygame.draw.polygon(shield_surf, (255, 255, 255), shield_points, 2)
        self.images['bonus_shield'] = shield_surf

    def create_environment(self):
        """Vytvoří prostředí - moře, mraky, ostrovy"""
        # Vytvoření mraků
        for i in range(8):
            cloud = {
                'x': random.randint(-100, SCREEN_WIDTH + 100),
                'y': random.randint(50, 200),
                'size': random.randint(30, 80),
                'speed': random.uniform(0.2, 0.8),
                'opacity': random.randint(100, 180)
            }
            self.clouds.append(cloud)

        # Vytvoření vln
        for i in range(20):
            wave = {
                'x': i * (SCREEN_WIDTH // 20),
                'amplitude': random.randint(5, 15),
                'frequency': random.uniform(0.02, 0.05),
                'phase': random.uniform(0, math.pi * 2)
            }
            self.waves.append(wave)

        # Vytvoření ostrovů
        for i in range(3):
            island = {
                'x': random.randint(100, SCREEN_WIDTH - 100),
                'y': random.randint(SCREEN_HEIGHT - 150, SCREEN_HEIGHT - 50),
                'size': random.randint(40, 100),
                'type': random.choice(['small', 'medium', 'large'])
            }
            self.islands.append(island)

    def run(self):
        """Hlavní herní smyčka"""
        while self.running:
            # Zpracování událostí
            self.handle_events()

            # Aktualizace podle stavu
            if self.current_state == GameState.MENU:
                self.update_menu()
            elif self.current_state == GameState.PLAYING:
                self.update_game()
            elif self.current_state == GameState.SETTINGS:
                self.update_settings()

            # Vykreslení
            self.render()

            # Udržení FPS
            self.clock.tick(FPS)
            self.frame_count += 1

        self.cleanup()

    def handle_events(self):
        """Zpracování událostí"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False

            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    if self.current_state == GameState.PLAYING:
                        self.current_state = GameState.MENU
                    else:
                        self.running = False

                # Menu navigace
                elif self.current_state == GameState.MENU:
                    if event.key == pygame.K_UP:
                        self.menu_selection = (self.menu_selection - 1) % len(self.menu_options)
                    elif event.key == pygame.K_DOWN:
                        self.menu_selection = (self.menu_selection + 1) % len(self.menu_options)
                    elif event.key == pygame.K_RETURN:
                        self.select_menu_option()

    def select_menu_option(self):
        """Vybere možnost z menu"""
        if self.menu_selection == 0:  # Nová hra
            self.current_state = GameState.PLAYING
            self.start_new_game()
        elif self.menu_selection == 1:  # Nastavení
            self.current_state = GameState.SETTINGS
        elif self.menu_selection == 2:  # Konec
            self.running = False

    def start_new_game(self):
        """Spustí novou hru"""
        self.player_pos = [SCREEN_WIDTH//2, SCREEN_HEIGHT//2]
        self.player_health = 100
        self.enemies = []
        self.bullets = []
        self.enemy_bullets = []
        self.score = 0
        self.level = 1
        self.enemy_spawn_timer = 0
        self.formation_timer = 0
        self.boss_active = False
        self.boss = None
        self.level_enemies_killed = 0
        self.enemies_spawned = 0
        self.bonuses = []
        self.bonus_spawn_timer = 0
        self.level_transition = False
        self.enemy_spawn_pause = False
        self.weapon_upgrade = False
        self.shield_active = False
        print("🚀 Nová hra spuštěna!")

    def update_menu(self):
        """Aktualizace menu"""
        pass  # Menu je statické

    def update_settings(self):
        """Aktualizace nastavení"""
        keys = pygame.key.get_pressed()
        if keys[pygame.K_ESCAPE]:
            self.current_state = GameState.MENU

    def update_game(self):
        """Aktualizace hry"""
        keys = pygame.key.get_pressed()

        # Pohyb hráče
        if keys[pygame.K_LEFT] and self.player_pos[0] > 20:
            self.player_pos[0] -= 5
        if keys[pygame.K_RIGHT] and self.player_pos[0] < SCREEN_WIDTH - 20:
            self.player_pos[0] += 5
        if keys[pygame.K_UP] and self.player_pos[1] > 20:
            self.player_pos[1] -= 5
        if keys[pygame.K_DOWN] and self.player_pos[1] < SCREEN_HEIGHT - 20:
            self.player_pos[1] += 5

        # Střelba
        if keys[pygame.K_SPACE] and self.frame_count % 10 == 0:
            if self.weapon_upgrade:
                # Dvojitá střelba
                self.bullets.append([self.player_pos[0] - 10, self.player_pos[1] - 20])
                self.bullets.append([self.player_pos[0] + 10, self.player_pos[1] - 20])
            else:
                # Normální střelba
                self.bullets.append([self.player_pos[0], self.player_pos[1] - 20])
            self.sounds['shoot'].play()

        # Aktualizace projektilů
        for bullet in self.bullets[:]:
            bullet[1] -= 10
            if bullet[1] < 0:
                self.bullets.remove(bullet)

        for bullet in self.enemy_bullets[:]:
            bullet[1] += 8
            if bullet[1] > SCREEN_HEIGHT:
                self.enemy_bullets.remove(bullet)

        # Spawn nepřátel
        self.spawn_enemies()

        # Aktualizace nepřátel
        self.update_enemies()

        # Aktualizace bosse
        if self.boss_active and self.boss:
            self.update_boss()

        # Aktualizace bonusů
        self.update_bonuses()

        # Aktualizace prostředí
        self.update_environment()

        # Aktualizace powerupů
        self.update_powerups()

        # Aktualizace přechodu
        self.update_level_transition()

        # Kolize
        self.check_collisions()

    def spawn_enemies(self):
        """Vytváření nepřátel"""
        if self.enemy_spawn_pause or self.boss_active:
            return

        self.enemy_spawn_timer += 1

        # Kontrola bosse
        if self.level_enemies_killed >= self.enemies_needed_for_boss:
            self.spawn_boss()
            return

        # Spawn nepřátel
        if self.enemy_spawn_timer >= 120 and len(self.enemies) < 5:
            enemy_type = random.choice(['fighter', 'bomber', 'interceptor'])
            enemy_x = random.randint(50, SCREEN_WIDTH - 50)

            enemy = {
                'type': enemy_type,
                'pos': [enemy_x, -30],
                'health': 30 if enemy_type == 'fighter' else 80 if enemy_type == 'bomber' else 20,
                'max_health': 30 if enemy_type == 'fighter' else 80 if enemy_type == 'bomber' else 20,
                'speed': 2.5 if enemy_type == 'fighter' else 1.5 if enemy_type == 'bomber' else 4.0,
                'shoot_timer': 0
            }

            self.enemies.append(enemy)
            self.enemy_spawn_timer = 0
            self.enemies_spawned += 1

    def update_enemies(self):
        """Aktualizace nepřátel"""
        for enemy in self.enemies[:]:
            # Pohyb
            enemy['pos'][1] += enemy['speed']

            # Střelba
            enemy['shoot_timer'] += 1
            if enemy['shoot_timer'] >= 60:  # Střelba každou sekundu
                self.enemy_bullets.append([enemy['pos'][0], enemy['pos'][1] + 20])
                enemy['shoot_timer'] = 0

            # Odstranění mimo obrazovku
            if enemy['pos'][1] > SCREEN_HEIGHT + 10:
                self.enemies.remove(enemy)
                self.level_enemies_killed += 1

    def spawn_boss(self):
        """Vytvoří bosse"""
        self.boss_active = True
        boss_types = ['giant_bomber', 'flying_fortress', 'ace_squadron', 'super_ace']
        boss_type = boss_types[min(self.level - 1, len(boss_types) - 1)]

        self.boss = {
            'type': boss_type,
            'pos': [SCREEN_WIDTH // 2, -80],
            'health': 200 + (self.level * 50),
            'max_health': 200 + (self.level * 50),
            'speed': 1.5,
            'shoot_timer': 0,
            'phase': 1,
            'direction': 1,
            'movement_pattern': 0
        }
        print(f"👑 BOSS SPAWN: {boss_type}!")

    def update_boss(self):
        """Aktualizace bosse"""
        if not self.boss:
            return

        # Pohyb bosse
        if self.boss['pos'][1] < 180:
            self.boss['pos'][1] += self.boss['speed']
        else:
            # Lineární pohyb ze strany na stranu
            self.boss['movement_pattern'] += 1
            speed = 2.0 + (self.boss['phase'] * 0.5)
            self.boss['pos'][0] += self.boss['direction'] * speed

            # Změna směru na krajích
            if self.boss['pos'][0] <= 60:
                self.boss['direction'] = 1
            elif self.boss['pos'][0] >= SCREEN_WIDTH - 60:
                self.boss['direction'] = -1

        # Střelba bosse
        self.boss['shoot_timer'] += 1
        if self.boss['shoot_timer'] >= 30:  # Rychlejší střelba
            # Trojitá střelba
            self.enemy_bullets.append([self.boss['pos'][0] - 20, self.boss['pos'][1] + 30])
            self.enemy_bullets.append([self.boss['pos'][0], self.boss['pos'][1] + 30])
            self.enemy_bullets.append([self.boss['pos'][0] + 20, self.boss['pos'][1] + 30])
            self.boss['shoot_timer'] = 0

        # Fáze podle zdraví
        health_ratio = self.boss['health'] / self.boss['max_health']
        if health_ratio > 0.66:
            self.boss['phase'] = 1
        elif health_ratio > 0.33:
            self.boss['phase'] = 2
        else:
            self.boss['phase'] = 3
