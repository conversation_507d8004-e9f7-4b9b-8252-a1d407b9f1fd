#!/usr/bin/env python3
"""
Pokročilá AI a nové typy nepřátel pro hru Spitfire
"""

import pygame
import math
import random
from enum import Enum
from typing import List, Tuple, Optional

class AIBehavior(Enum):
    PATROL = "patrol"
    CHASE = "chase"
    FLEE = "flee"
    ATTACK = "attack"
    FORMATION = "formation"
    KAMIKAZE = "kamikaze"
    DEFENSIVE = "defensive"

class EnemyType(Enum):
    FIGHTER = "fighter"
    BOMBER = "bomber"
    INTERCEPTOR = "interceptor"
    SCOUT = "scout"
    ACE = "ace"
    KAMIKAZE = "kamikaze"
    HEAVY_BOMBER = "heavy_bomber"

class AdvancedEnemy(pygame.sprite.Sprite):
    """Pokročilý nepřítel s inteligentní AI"""
    
    def __init__(self, x, y, enemy_type: EnemyType):
        super().__init__()
        self.enemy_type = enemy_type
        self.setup_enemy_stats()
        
        # AI systém
        self.behavior = AIBehavior.PATROL
        self.target = None
        self.formation_leader = None
        self.formation_offset = pygame.math.Vector2(0, 0)
        
        # Pozice a pohyb
        self.position = pygame.math.Vector2(x, y)
        self.velocity = pygame.math.Vector2(0, 0)
        self.acceleration = pygame.math.Vector2(0, 0)
        self.max_speed = self.base_speed
        self.max_force = 0.3
        
        # AI parametry
        self.detection_range = 150
        self.attack_range = 100
        self.flee_threshold = 20  # Zdraví, při kterém začne utíkat
        self.reaction_time = random.randint(30, 60)  # Frames
        self.last_decision = 0
        
        # Střelba
        self.shoot_cooldown = 0
        self.shoot_delay = random.randint(60, 120)
        self.accuracy = 0.7  # Přesnost střelby
        
        # Manévry
        self.maneuver_timer = 0
        self.current_maneuver = None
        self.maneuver_data = {}
        
        # Vytvoření sprite
        self.create_sprite()
        self.rect = self.image.get_rect(center=(x, y))
    
    def setup_enemy_stats(self):
        """Nastaví statistiky podle typu nepřítele"""
        stats = {
            EnemyType.FIGHTER: {
                'health': 30, 'speed': 2.5, 'damage': 15, 'score': 100,
                'color': (200, 50, 50), 'size': (30, 20)
            },
            EnemyType.BOMBER: {
                'health': 80, 'speed': 1.5, 'damage': 25, 'score': 300,
                'color': (100, 100, 200), 'size': (50, 30)
            },
            EnemyType.INTERCEPTOR: {
                'health': 20, 'speed': 4.0, 'damage': 10, 'score': 150,
                'color': (200, 200, 50), 'size': (25, 15)
            },
            EnemyType.SCOUT: {
                'health': 15, 'speed': 3.5, 'damage': 5, 'score': 80,
                'color': (150, 150, 150), 'size': (20, 15)
            },
            EnemyType.ACE: {
                'health': 50, 'speed': 3.0, 'damage': 20, 'score': 500,
                'color': (255, 100, 0), 'size': (35, 25)
            },
            EnemyType.KAMIKAZE: {
                'health': 25, 'speed': 3.5, 'damage': 50, 'score': 200,
                'color': (255, 0, 0), 'size': (25, 20)
            },
            EnemyType.HEAVY_BOMBER: {
                'health': 150, 'speed': 1.0, 'damage': 40, 'score': 800,
                'color': (80, 80, 120), 'size': (70, 40)
            }
        }
        
        enemy_stats = stats[self.enemy_type]
        self.max_health = enemy_stats['health']
        self.health = self.max_health
        self.base_speed = enemy_stats['speed']
        self.damage = enemy_stats['damage']
        self.score_value = enemy_stats['score']
        self.color = enemy_stats['color']
        self.size = enemy_stats['size']
    
    def create_sprite(self):
        """Vytvoří sprite nepřítele"""
        self.image = pygame.Surface(self.size, pygame.SRCALPHA)
        
        # Různé tvary podle typu
        if self.enemy_type == EnemyType.FIGHTER:
            # Stíhačka - trojúhelník
            points = [(self.size[0]//2, 0), (0, self.size[1]), (self.size[0], self.size[1])]
            pygame.draw.polygon(self.image, self.color, points)
        
        elif self.enemy_type == EnemyType.BOMBER:
            # Bombardér - obdélník s křídly
            pygame.draw.rect(self.image, self.color, (10, 5, 30, 20))
            pygame.draw.rect(self.image, self.color, (0, 12, 50, 6))
        
        elif self.enemy_type == EnemyType.INTERCEPTOR:
            # Interceptor - úzký trojúhelník
            points = [(self.size[0]//2, 0), (5, self.size[1]), (self.size[0]-5, self.size[1])]
            pygame.draw.polygon(self.image, self.color, points)
        
        elif self.enemy_type == EnemyType.ACE:
            # Eso - hvězda
            self.draw_star(self.image, self.size[0]//2, self.size[1]//2, 
                          self.size[0]//3, self.color)
        
        else:
            # Výchozí - elipsa
            pygame.draw.ellipse(self.image, self.color, (0, 0, *self.size))
        
        # Okraj
        pygame.draw.rect(self.image, (255, 255, 255), (0, 0, *self.size), 1)
    
    def draw_star(self, surface, x, y, radius, color):
        """Nakreslí hvězdu"""
        points = []
        for i in range(10):
            angle = i * math.pi / 5
            if i % 2 == 0:
                r = radius
            else:
                r = radius // 2
            px = x + r * math.cos(angle - math.pi/2)
            py = y + r * math.sin(angle - math.pi/2)
            points.append((px, py))
        pygame.draw.polygon(surface, color, points)
    
    def update(self, player_pos, allies, enemies):
        """Hlavní update funkce"""
        self.update_ai(player_pos, allies, enemies)
        self.update_movement()
        self.update_shooting()
        self.update_maneuvers()
        
        # Aktualizace pozice
        self.rect.center = (int(self.position.x), int(self.position.y))
        
        # Odstranění, pokud je mimo obrazovku
        if (self.position.y > 700 or self.position.y < -100 or 
            self.position.x < -100 or self.position.x > 900):
            self.kill()
    
    def update_ai(self, player_pos, allies, enemies):
        """Aktualizuje AI rozhodování"""
        current_time = pygame.time.get_ticks()
        
        # Rozhodování pouze každých několik framů
        if current_time - self.last_decision < self.reaction_time:
            return
        
        self.last_decision = current_time
        
        # Vzdálenost k hráči
        player_distance = self.position.distance_to(player_pos)
        
        # Rozhodování podle typu a situace
        if self.enemy_type == EnemyType.KAMIKAZE:
            self.behavior = AIBehavior.KAMIKAZE
            self.target = player_pos
        
        elif self.health < self.flee_threshold:
            self.behavior = AIBehavior.FLEE
            self.target = player_pos
        
        elif player_distance < self.detection_range:
            if self.enemy_type == EnemyType.SCOUT:
                # Scout utíká a varuje ostatní
                self.behavior = AIBehavior.FLEE
                self.alert_nearby_enemies(enemies)
            elif player_distance < self.attack_range:
                self.behavior = AIBehavior.ATTACK
                self.target = player_pos
            else:
                self.behavior = AIBehavior.CHASE
                self.target = player_pos
        
        else:
            self.behavior = AIBehavior.PATROL
    
    def update_movement(self):
        """Aktualizuje pohyb podle AI chování"""
        if self.behavior == AIBehavior.PATROL:
            self.patrol_movement()
        elif self.behavior == AIBehavior.CHASE:
            self.chase_movement()
        elif self.behavior == AIBehavior.FLEE:
            self.flee_movement()
        elif self.behavior == AIBehavior.ATTACK:
            self.attack_movement()
        elif self.behavior == AIBehavior.KAMIKAZE:
            self.kamikaze_movement()
        elif self.behavior == AIBehavior.FORMATION:
            self.formation_movement()
        
        # Aplikace fyziky
        self.velocity += self.acceleration
        if self.velocity.length() > self.max_speed:
            self.velocity.scale_to_length(self.max_speed)
        
        self.position += self.velocity
        self.acceleration *= 0  # Reset acceleration
    
    def patrol_movement(self):
        """Pohyb při hlídkování"""
        # Sinusový pohyb dolů
        self.acceleration.y = 0.1
        self.acceleration.x = 0.5 * math.sin(pygame.time.get_ticks() * 0.01)
    
    def chase_movement(self):
        """Pohyb při pronásledování"""
        if self.target:
            desired = pygame.math.Vector2(self.target) - self.position
            if desired.length() > 0:
                desired.normalize_ip()
                desired *= self.max_speed
                
                steer = desired - self.velocity
                if steer.length() > self.max_force:
                    steer.scale_to_length(self.max_force)
                
                self.acceleration += steer
    
    def flee_movement(self):
        """Pohyb při útěku"""
        if self.target:
            desired = self.position - pygame.math.Vector2(self.target)
            if desired.length() > 0:
                desired.normalize_ip()
                desired *= self.max_speed
                
                steer = desired - self.velocity
                if steer.length() > self.max_force:
                    steer.scale_to_length(self.max_force)
                
                self.acceleration += steer
    
    def attack_movement(self):
        """Pohyb při útoku"""
        if self.target:
            # Kruhový pohyb kolem cíle
            to_target = pygame.math.Vector2(self.target) - self.position
            if to_target.length() > 0:
                # Perpendicular vector for circling
                perp = pygame.math.Vector2(-to_target.y, to_target.x)
                perp.normalize_ip()
                
                desired = perp * self.max_speed
                steer = desired - self.velocity
                if steer.length() > self.max_force:
                    steer.scale_to_length(self.max_force)
                
                self.acceleration += steer
    
    def kamikaze_movement(self):
        """Pohyb kamikaze - přímý útok"""
        if self.target:
            desired = pygame.math.Vector2(self.target) - self.position
            if desired.length() > 0:
                desired.normalize_ip()
                desired *= self.max_speed * 1.5  # Rychlejší
                
                self.acceleration = desired - self.velocity
    
    def formation_movement(self):
        """Pohyb ve formaci"""
        if self.formation_leader:
            target_pos = self.formation_leader.position + self.formation_offset
            desired = target_pos - self.position
            if desired.length() > 0:
                desired.normalize_ip()
                desired *= self.max_speed
                
                steer = desired - self.velocity
                if steer.length() > self.max_force:
                    steer.scale_to_length(self.max_force)
                
                self.acceleration += steer
    
    def update_shooting(self):
        """Aktualizuje střelbu"""
        if self.shoot_cooldown > 0:
            self.shoot_cooldown -= 1
            return
        
        if (self.behavior in [AIBehavior.ATTACK, AIBehavior.CHASE] and 
            self.target and random.random() < 0.02):  # 2% šance každý frame
            
            self.shoot()
            self.shoot_cooldown = self.shoot_delay + random.randint(-20, 20)
    
    def shoot(self):
        """Vystřelí projektil"""
        if not self.target:
            return
        
        # Predikce pozice hráče
        target_pos = pygame.math.Vector2(self.target)
        
        # Přidání nepřesnosti podle typu nepřítele
        if self.enemy_type != EnemyType.ACE:
            inaccuracy = (1.0 - self.accuracy) * 50
            target_pos.x += random.uniform(-inaccuracy, inaccuracy)
            target_pos.y += random.uniform(-inaccuracy, inaccuracy)
        
        # Vytvoření projektilu (bude implementováno v hlavní hře)
        direction = target_pos - self.position
        if direction.length() > 0:
            direction.normalize_ip()
            
            # Zde by se vytvořil projektil
            print(f"{self.enemy_type.value} střílí!")
    
    def update_maneuvers(self):
        """Aktualizuje speciální manévry"""
        if self.current_maneuver:
            self.maneuver_timer -= 1
            
            if self.current_maneuver == "barrel_roll":
                self.barrel_roll_maneuver()
            elif self.current_maneuver == "dive_bomb":
                self.dive_bomb_maneuver()
            
            if self.maneuver_timer <= 0:
                self.current_maneuver = None
    
    def start_maneuver(self, maneuver_type):
        """Spustí speciální manévr"""
        self.current_maneuver = maneuver_type
        self.maneuver_timer = 120  # 2 sekundy při 60 FPS
        self.maneuver_data = {}
    
    def barrel_roll_maneuver(self):
        """Manévr barrel roll"""
        if 'start_pos' not in self.maneuver_data:
            self.maneuver_data['start_pos'] = self.position.copy()
        
        progress = 1.0 - (self.maneuver_timer / 120.0)
        angle = progress * 2 * math.pi
        
        offset_x = 30 * math.sin(angle)
        self.position.x = self.maneuver_data['start_pos'].x + offset_x
    
    def dive_bomb_maneuver(self):
        """Manévr dive bomb"""
        self.acceleration.y += 0.5  # Rychlé klesání
    
    def alert_nearby_enemies(self, enemies):
        """Varuje blízké nepřátele"""
        for enemy in enemies:
            if (enemy != self and 
                isinstance(enemy, AdvancedEnemy) and
                self.position.distance_to(enemy.position) < 100):
                enemy.behavior = AIBehavior.CHASE
                enemy.target = self.target
    
    def hit(self, damage):
        """Zpracuje zásah"""
        self.health -= damage
        
        # Reakce na zásah
        if self.health > 0:
            # Manévr při zásahu
            if random.random() < 0.3:  # 30% šance
                self.start_maneuver("barrel_roll")
        else:
            # Smrt
            self.kill()
            return self.score_value
        
        return 0

class FormationManager:
    """Správce formací nepřátel"""
    
    def __init__(self):
        self.formations = []
    
    def create_v_formation(self, leader_pos, num_enemies=5):
        """Vytvoří V formaci"""
        formation = []
        leader = AdvancedEnemy(leader_pos[0], leader_pos[1], EnemyType.FIGHTER)
        formation.append(leader)
        
        # Vytvoření následovníků
        for i in range(1, num_enemies):
            side = 1 if i % 2 == 1 else -1
            rank = (i + 1) // 2
            
            offset_x = side * rank * 40
            offset_y = rank * 30
            
            follower = AdvancedEnemy(leader_pos[0] + offset_x, leader_pos[1] + offset_y, 
                                   EnemyType.FIGHTER)
            follower.behavior = AIBehavior.FORMATION
            follower.formation_leader = leader
            follower.formation_offset = pygame.math.Vector2(offset_x, offset_y)
            
            formation.append(follower)
        
        self.formations.append(formation)
        return formation
