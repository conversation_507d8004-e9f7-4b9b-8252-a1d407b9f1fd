#!/usr/bin/env python3
"""
Spitfire Game Launcher
"""

import os
import sys
import subprocess

def main():
    print("🚀 Spitfire Game Launcher")
    print("=" * 40)
    
    versions = []
    
    if os.path.exists('spitfire_improved.py'):
        versions.append(('<PERSON><PERSON>pšená verze', 'spitfire_improved.py'))
    
    if os.path.exists('spitfire_game.py'):
        versions.append(('Původní verze', 'spitfire_game.py'))
    
    if not versions:
        print("❌ Žádná verze hry nebyla nalezena!")
        return 1
    
    print("Dostupné verze:")
    for i, (name, file) in enumerate(versions, 1):
        print(f"{i}. {name}")
    
    try:
        choice = input(f"\nVyberte verzi (1-{len(versions)}): ")
        choice_idx = int(choice) - 1
        
        if 0 <= choice_idx < len(versions):
            selected_name, selected_file = versions[choice_idx]
            print(f"\n🎮 Spouštím {selected_name}...")
            subprocess.run([sys.executable, selected_file])
        else:
            print("❌ Neplatná volba!")
            return 1
            
    except (ValueError, KeyboardInterrupt):
        print("\n👋 Ukončeno")
        return 0
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
