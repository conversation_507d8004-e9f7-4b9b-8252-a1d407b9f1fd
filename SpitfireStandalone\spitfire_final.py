#!/usr/bin/env python3
"""
SPITFIRE - Finální standalone verze pro distribuci
Kompletní hra s všemi funkcemi v jednom souboru
"""

import pygame
import sys
import math
import random
import os

# Konstanty
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Barvy
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)

class SpitfireFinal:
    def __init__(self):
        pygame.init()
        pygame.mixer.init()
        
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Spitfire - Standalone Edition")
        self.clock = pygame.time.Clock()
        
        # Herní stav
        self.running = True
        self.game_state = "menu"  # menu, playing, game_over
        self.frame_count = 0
        
        # <PERSON>r<PERSON><PERSON>
        self.player_pos = [SCREEN_WIDTH//2, SCREEN_HEIGHT//2]
        self.player_health = 100
        self.score = 0
        self.level = 1
        
        # Herní objekty
        self.enemies = []
        self.bullets = []
        self.enemy_bullets = []
        self.bonuses = []
        self.boss = None
        self.boss_active = False
        
        # Timery
        self.enemy_spawn_timer = 0
        self.bonus_spawn_timer = 0
        self.level_enemies_killed = 0
        self.enemies_needed_for_boss = 10
        
        # Bonusy
        self.weapon_upgrade = False
        self.weapon_timer = 0
        self.shield_active = False
        self.shield_timer = 0
        
        # Prostředí
        self.environment_offset = 0
        self.waves = []
        self.islands = []
        
        # Menu
        self.menu_selection = 0
        self.menu_options = ["Nová hra", "Konec"]
        
        # Načtení assetů
        self.load_assets()
        self.create_environment()
        
        print("🎮 Spitfire Final Edition spuštěna!")

    def load_assets(self):
        """Načte grafiku a zvuky"""
        # Tiché zvuky pro standalone
        self.sounds = {
            'shoot': pygame.mixer.Sound(buffer=bytearray([])),
            'explosion': pygame.mixer.Sound(buffer=bytearray([])),
            'hit': pygame.mixer.Sound(buffer=bytearray([])),
            'powerup': pygame.mixer.Sound(buffer=bytearray([]))
        }
        
        # Vytvoření grafiky
        self.images = {}
        self.create_graphics()

    def create_graphics(self):
        """Vytvoří grafiku programově"""
        # Hráč - Spitfire
        player_surf = pygame.Surface((40, 30), pygame.SRCALPHA)
        pygame.draw.ellipse(player_surf, (100, 100, 100), (15, 5, 10, 20))
        pygame.draw.ellipse(player_surf, (80, 80, 80), (0, 12, 40, 6))
        pygame.draw.circle(player_surf, (50, 50, 150), (20, 10), 3)
        self.images['player'] = player_surf
        
        # Nepřátelé
        fighter_surf = pygame.Surface((32, 28), pygame.SRCALPHA)
        pygame.draw.ellipse(fighter_surf, (120, 120, 120), (12, 8, 8, 16))
        pygame.draw.ellipse(fighter_surf, (100, 100, 100), (2, 12, 28, 6))
        self.images['fighter'] = fighter_surf
        
        bomber_surf = pygame.Surface((50, 35), pygame.SRCALPHA)
        pygame.draw.ellipse(bomber_surf, (80, 80, 120), (18, 10, 14, 18))
        pygame.draw.ellipse(bomber_surf, (70, 70, 110), (0, 16, 50, 8))
        self.images['bomber'] = bomber_surf
        
        # Boss
        boss_surf = pygame.Surface((80, 60), pygame.SRCALPHA)
        pygame.draw.ellipse(boss_surf, (60, 60, 100), (30, 20, 20, 30))
        pygame.draw.ellipse(boss_surf, (50, 50, 90), (0, 28, 80, 12))
        self.images['boss'] = boss_surf
        
        # Bonusy
        health_surf = pygame.Surface((20, 20), pygame.SRCALPHA)
        pygame.draw.rect(health_surf, (255, 50, 50), (6, 2, 8, 16))
        pygame.draw.rect(health_surf, (255, 50, 50), (2, 6, 16, 8))
        self.images['bonus_health'] = health_surf
        
        weapon_surf = pygame.Surface((20, 20), pygame.SRCALPHA)
        pygame.draw.polygon(weapon_surf, (50, 255, 50), [(10, 2), (6, 8), (8, 8), (8, 18), (12, 18), (12, 8), (14, 8)])
        self.images['bonus_weapon'] = weapon_surf
        
        shield_surf = pygame.Surface((20, 20), pygame.SRCALPHA)
        shield_points = [(10, 2), (16, 5), (16, 12), (10, 18), (4, 12), (4, 5)]
        pygame.draw.polygon(shield_surf, (50, 50, 255), shield_points)
        self.images['bonus_shield'] = shield_surf

    def create_environment(self):
        """Vytvoří prostředí"""
        # Vlny
        for i in range(15):
            wave = {
                'x': i * (SCREEN_WIDTH // 15),
                'amplitude': random.randint(3, 8),
                'frequency': random.uniform(0.02, 0.04),
                'phase': random.uniform(0, math.pi * 2)
            }
            self.waves.append(wave)
        
        # Ostrovy
        for i in range(2):
            island = {
                'x': random.randint(100, SCREEN_WIDTH - 100),
                'y': random.randint(SCREEN_HEIGHT - 100, SCREEN_HEIGHT - 50),
                'size': random.randint(30, 60)
            }
            self.islands.append(island)

    def run(self):
        """Hlavní herní smyčka"""
        while self.running:
            self.handle_events()
            
            if self.game_state == "menu":
                self.update_menu()
            elif self.game_state == "playing":
                self.update_game()
            
            self.render()
            self.clock.tick(FPS)
            self.frame_count += 1
        
        pygame.quit()
        sys.exit()

    def handle_events(self):
        """Zpracování událostí"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    if self.game_state == "playing":
                        self.game_state = "menu"
                    else:
                        self.running = False
                elif self.game_state == "menu":
                    if event.key == pygame.K_UP:
                        self.menu_selection = (self.menu_selection - 1) % len(self.menu_options)
                    elif event.key == pygame.K_DOWN:
                        self.menu_selection = (self.menu_selection + 1) % len(self.menu_options)
                    elif event.key == pygame.K_RETURN:
                        if self.menu_selection == 0:
                            self.start_new_game()
                        else:
                            self.running = False

    def start_new_game(self):
        """Spustí novou hru"""
        self.game_state = "playing"
        self.player_pos = [SCREEN_WIDTH//2, SCREEN_HEIGHT//2]
        self.player_health = 100
        self.score = 0
        self.level = 1
        self.enemies = []
        self.bullets = []
        self.enemy_bullets = []
        self.bonuses = []
        self.boss = None
        self.boss_active = False
        self.level_enemies_killed = 0
        self.weapon_upgrade = False
        self.shield_active = False

    def update_menu(self):
        """Aktualizace menu"""
        pass

    def update_game(self):
        """Aktualizace hry"""
        keys = pygame.key.get_pressed()
        
        # Pohyb hráče
        if keys[pygame.K_LEFT] and self.player_pos[0] > 20:
            self.player_pos[0] -= 5
        if keys[pygame.K_RIGHT] and self.player_pos[0] < SCREEN_WIDTH - 20:
            self.player_pos[0] += 5
        if keys[pygame.K_UP] and self.player_pos[1] > 20:
            self.player_pos[1] -= 5
        if keys[pygame.K_DOWN] and self.player_pos[1] < SCREEN_HEIGHT - 20:
            self.player_pos[1] += 5
        
        # Střelba
        if keys[pygame.K_SPACE] and self.frame_count % 10 == 0:
            if self.weapon_upgrade:
                self.bullets.append([self.player_pos[0] - 10, self.player_pos[1] - 20])
                self.bullets.append([self.player_pos[0] + 10, self.player_pos[1] - 20])
            else:
                self.bullets.append([self.player_pos[0], self.player_pos[1] - 20])
        
        # Aktualizace projektilů
        for bullet in self.bullets[:]:
            bullet[1] -= 10
            if bullet[1] < 0:
                self.bullets.remove(bullet)
        
        for bullet in self.enemy_bullets[:]:
            bullet[1] += 6
            if bullet[1] > SCREEN_HEIGHT:
                self.enemy_bullets.remove(bullet)
        
        # Spawn nepřátel
        self.spawn_enemies()
        
        # Aktualizace nepřátel
        self.update_enemies()
        
        # Aktualizace bosse
        if self.boss_active:
            self.update_boss()
        
        # Aktualizace bonusů
        self.update_bonuses()
        
        # Aktualizace prostředí
        self.update_environment()
        
        # Aktualizace powerupů
        self.update_powerups()
        
        # Kolize
        self.check_collisions()

    def spawn_enemies(self):
        """Spawn nepřátel"""
        if self.boss_active:
            return
        
        self.enemy_spawn_timer += 1
        
        if self.level_enemies_killed >= self.enemies_needed_for_boss:
            self.spawn_boss()
            return
        
        if self.enemy_spawn_timer >= 90 and len(self.enemies) < 4:
            enemy_type = random.choice(['fighter', 'bomber'])
            enemy = {
                'type': enemy_type,
                'pos': [random.randint(50, SCREEN_WIDTH - 50), -30],
                'health': 30 if enemy_type == 'fighter' else 60,
                'max_health': 30 if enemy_type == 'fighter' else 60,
                'speed': 2.5 if enemy_type == 'fighter' else 1.5,
                'shoot_timer': 0
            }
            self.enemies.append(enemy)
            self.enemy_spawn_timer = 0

    def update_enemies(self):
        """Aktualizace nepřátel"""
        for enemy in self.enemies[:]:
            enemy['pos'][1] += enemy['speed']
            
            enemy['shoot_timer'] += 1
            if enemy['shoot_timer'] >= 80:
                self.enemy_bullets.append([enemy['pos'][0], enemy['pos'][1] + 20])
                enemy['shoot_timer'] = 0
            
            if enemy['pos'][1] > SCREEN_HEIGHT + 10:
                self.enemies.remove(enemy)
                self.level_enemies_killed += 1

    def spawn_boss(self):
        """Spawn bosse"""
        self.boss_active = True
        self.boss = {
            'pos': [SCREEN_WIDTH // 2, -60],
            'health': 150 + (self.level * 30),
            'max_health': 150 + (self.level * 30),
            'speed': 1.5,
            'shoot_timer': 0,
            'direction': 1
        }

    def update_boss(self):
        """Aktualizace bosse"""
        if not self.boss:
            return
        
        # Pohyb
        if self.boss['pos'][1] < 150:
            self.boss['pos'][1] += self.boss['speed']
        else:
            self.boss['pos'][0] += self.boss['direction'] * 2
            if self.boss['pos'][0] <= 60 or self.boss['pos'][0] >= SCREEN_WIDTH - 60:
                self.boss['direction'] *= -1
        
        # Střelba
        self.boss['shoot_timer'] += 1
        if self.boss['shoot_timer'] >= 40:
            self.enemy_bullets.append([self.boss['pos'][0] - 15, self.boss['pos'][1] + 30])
            self.enemy_bullets.append([self.boss['pos'][0], self.boss['pos'][1] + 30])
            self.enemy_bullets.append([self.boss['pos'][0] + 15, self.boss['pos'][1] + 30])
            self.boss['shoot_timer'] = 0

    def update_bonuses(self):
        """Aktualizace bonusů"""
        self.bonus_spawn_timer += 1
        
        if self.bonus_spawn_timer >= 600:  # 10 sekund
            bonus_type = random.choice(['health', 'weapon', 'shield'])
            bonus = {
                'type': bonus_type,
                'pos': [random.randint(50, SCREEN_WIDTH - 50), -20]
            }
            self.bonuses.append(bonus)
            self.bonus_spawn_timer = 0
        
        for bonus in self.bonuses[:]:
            bonus['pos'][1] += 2
            if bonus['pos'][1] > SCREEN_HEIGHT:
                self.bonuses.remove(bonus)

    def update_environment(self):
        """Aktualizace prostředí"""
        self.environment_offset += 1
        
        for island in self.islands:
            island['y'] += 0.5
            if island['y'] > SCREEN_HEIGHT + 50:
                island['y'] = -50
                island['x'] = random.randint(100, SCREEN_WIDTH - 100)

    def update_powerups(self):
        """Aktualizace powerupů"""
        if self.weapon_upgrade:
            self.weapon_timer += 1
            if self.weapon_timer >= 600:  # 10 sekund
                self.weapon_upgrade = False
                self.weapon_timer = 0
        
        if self.shield_active:
            self.shield_timer += 1
            if self.shield_timer >= 600:  # 10 sekund
                self.shield_active = False
                self.shield_timer = 0
