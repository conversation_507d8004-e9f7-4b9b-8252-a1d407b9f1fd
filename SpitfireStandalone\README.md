# Spitfire - Standalone Edition

## 🎮 O hře

Spitfire je letecká akční hra inspirovaná klasickými shoot-em-up hrami z 90. let. Hrajete za pilota Spitfire během druhé světové války a bojujete proti vlnám německých letadel.

## ✨ Funkce

### 🎯 Herní mechaniky:
- **4 typy nepř<PERSON>tel**: Fighter, Bomber, Interceptor, Ace
- **4 unikátní bossové** s různými útoky a fázemi
- **Progresivní obtížnost** - každý level je těžší
- **Delší levely** - 15-30 nepřátel před bossem
- **Úplná obnova života** mezi levely

### 🎁 Bonus systém:
- **❤️ Health**: +25 HP (max 100)
- **⭐ Score**: +500-800 bodů podle úrovně  
- **🔫 Weapon**: Dvojitá střelba na 10 sekund
- **🛡️ Shield**: Úplná imunita na 10 sekund

### 🌊 Prostředí:
- **<PERSON><PERSON><PERSON> shora** (top-down view)
- **An<PERSON><PERSON><PERSON> mo<PERSON>** s vlnami
- **Tropické ostrovy** s vegetací
- **Stíny mraků** na vodní hladině

### 🎬 Přechody mezi levely:
- **5 sekund odpočinku** po poražení bosse
- **Úplná obnova života** na 100%
- **Pauza ve spawnu** nepřátel
- **Krásné animace** a progress bar

## 🎮 Ovládání

- **Šipky**: Pohyb letadla
- **Mezerník**: Střelba
- **ESC**: Návrat do menu / Ukončení
- **Enter**: Výběr v menu
- **F1**: Nápověda (v plné verzi)
- **F11**: Fullscreen (v plné verzi)

## 🚀 Spuštění

### Požadavky:
- Python 3.7+
- pygame

### Instalace:
```bash
pip install pygame
```

### Spuštění:
```bash
python spitfire_standalone_complete.py
```

## 🎯 Tipy pro hru

1. **Sbírejte bonusy** - padají každých 10 sekund
2. **Weapon bonus** - ideální proti skupinám nepřátel
3. **Shield bonus** - použijte před nebezpečnými situacemi
4. **Bossové mají 3 fáze** - podle zdraví se mění chování
5. **Život se obnoví** mezi levely - můžete riskovat

## 🏆 Bossové

### Level 1: Giant Bomber
- **200 HP** - Těžký bombardér
- **Lineární pohyb** ze strany na stranu
- **Postupně rychlejší** podle fáze

### Level 2: Flying Fortress  
- **250 HP** - Létající pevnost
- **Opravený pohyb** - už se nezasekává
- **Trojitá střelba** ve všech fázích

### Level 3: Ace Squadron
- **300 HP** - Eskadra es
- **Rychlý lineární pohyb**
- **Agresivní útoky** ve vyšších fázích

### Level 4+: Super Ace
- **350+ HP** - Progresivně silnější
- **Kombinace útoků** všech předchozích bossů
- **Nekonečné levely** s rostoucí obtížností

## 🔧 Technické informace

### Optimalizace:
- **60 FPS** stabilní výkon
- **Efektivní kolize** - jen viditelné objekty
- **Smart spawning** - vybalancované nepřátele
- **Debug systém** pro sledování problémů

### Grafika:
- **Programově generovaná** - funguje bez externích souborů
- **Pixel art styl** - detailní letadla
- **Animované prostředí** - vlny, mraky, ostrovy
- **Vizuální efekty** - blikání bonusů, health bary

## 📦 Obsah balíčku

```
SpitfireStandalone/
├── spitfire_standalone_complete.py  # Kompletní hra
├── README.md                        # Tento soubor
└── requirements.txt                 # Závislosti
```

## 🐛 Známé opravy

- ✅ **Flying Fortress** se už nezasekává vpravo
- ✅ **Obnova života** funguje správně (100%)
- ✅ **Weapon bonus** - viditelná dvojitá střelba
- ✅ **Shield bonus** - skutečná imunita
- ✅ **Neviditelní nepřátelé** - opraveno
- ✅ **Problikávání ostrovů** - stabilní grafika

## 🎵 Audio

Hra obsahuje fallback tiché zvuky pro standalone verzi. Pro plný audio zážitek:

1. Vytvořte složku `assets/sounds/`
2. Přidejte soubory: `shoot.wav`, `explosion.wav`, `hit.wav`, `powerup.wav`
3. Vytvořte složku `assets/music/`
4. Přidejte soubor: `battle_music.wav`

## 🏅 High Score

Zkuste dosáhnout co nejvyššího skóre:
- **Nepřátelé**: 50-200 bodů podle typu
- **Bossové**: 1000-2000 bodů podle úrovně
- **Bonusy**: 200-800 extra bodů
- **Survival bonus**: Body za přežití

## 📞 Podpora

Pokud narazíte na problémy:
1. Zkontrolujte, že máte Python 3.7+
2. Nainstalujte pygame: `pip install pygame`
3. Spusťte z příkazové řádky pro debug výpisy

## 🎉 Užijte si hru!

Spitfire je kompletní retro letecká akce s moderními vylepšeními. Porazte všechny bosse, sbírejte bonusy a staňte se legendárním pilotem!

**Hodně štěstí v boji! ✈️🎯**
