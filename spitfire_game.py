#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Spitfire - Letecká arkádová hra
-------------------------------
Ovl<PERSON>dání:
- Šip<PERSON>: <PERSON><PERSON><PERSON> letadla
- Mezerník: Střelba
- P: Pauza/Pokračování
- Esc: Ukončení hry
"""

import pygame
import sys
import random
import os
import math

# Inicializace Pygame
pygame.init()
pygame.mixer.init()

# Konstanty
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Barvy
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
GRAY = (150, 150, 150)
ORANGE = (255, 165, 0)
PURPLE = (128, 0, 128)
CYAN = (0, 255, 255)
DARK_BLUE = (0, 0, 139)
LIGHT_BLUE = (173, 216, 230)
GOLD = (255, 215, 0)
SILVER = (192, 192, 192)

# Vytvoření herního okna
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Spitfire")
clock = pygame.time.Clock()

# Globální proměnné pro screen shake
screen_shake_intensity = 0
screen_shake_duration = 0

# Vytvoření adresářů pro assety, pokud neexistují
assets_dir = os.path.join(os.path.dirname(__file__), "assets")
images_dir = os.path.join(assets_dir, "images")
sounds_dir = os.path.join(assets_dir, "sounds")
music_dir = os.path.join(assets_dir, "music")

for directory in [assets_dir, images_dir, sounds_dir, music_dir]:
    if not os.path.exists(directory):
        os.makedirs(directory)

# Načtení obrázků (placeholder)
def load_image(name, scale=1):
    try:
        image = pygame.image.load(os.path.join(images_dir, name))
        if scale != 1:
            new_size = (int(image.get_width() * scale), int(image.get_height() * scale))
            image = pygame.transform.scale(image, new_size)
        return image.convert_alpha()
    except pygame.error:
        # Vytvoření placeholder obrázku, pokud soubor neexistuje
        surf = pygame.Surface((50, 50), pygame.SRCALPHA)
        pygame.draw.rect(surf, RED, surf.get_rect(), 2)
        return surf

# Načtení zvuků (placeholder)
def load_sound(name):
    try:
        return pygame.mixer.Sound(os.path.join(sounds_dir, name))
    except (pygame.error, FileNotFoundError):
        # Tichý zvuk, pokud soubor neexistuje
        print(f"Zvukový soubor {name} nebyl nalezen. Používám tichý zvuk.")
        return pygame.mixer.Sound(buffer=bytearray([]))

# Funkce pro screen shake
def add_screen_shake(intensity, duration):
    global screen_shake_intensity, screen_shake_duration
    screen_shake_intensity = max(screen_shake_intensity, intensity)
    screen_shake_duration = max(screen_shake_duration, duration)

def get_screen_offset():
    global screen_shake_intensity, screen_shake_duration
    if screen_shake_duration > 0:
        offset_x = random.randint(-screen_shake_intensity, screen_shake_intensity)
        offset_y = random.randint(-screen_shake_intensity, screen_shake_intensity)
        screen_shake_duration -= 1
        if screen_shake_duration <= 0:
            screen_shake_intensity = 0
        return offset_x, offset_y
    return 0, 0

# Třída pro scrollující pozadí
class ScrollingBackground:
    def __init__(self, image_path, speed):
        self.image = load_image(image_path)
        if self.image.get_width() < SCREEN_WIDTH:
            self.image = pygame.transform.scale(self.image, (SCREEN_WIDTH, self.image.get_height()))
        self.speed = speed
        self.y1 = 0
        self.y2 = -self.image.get_height()

    def update(self):
        self.y1 += self.speed
        self.y2 += self.speed

        if self.y1 > SCREEN_HEIGHT:
            self.y1 = self.y2 - self.image.get_height()

        if self.y2 > SCREEN_HEIGHT:
            self.y2 = self.y1 - self.image.get_height()

    def draw(self, surface):
        surface.blit(self.image, (0, self.y1))
        surface.blit(self.image, (0, self.y2))

# Třída pro hráčovo letadlo
class Player(pygame.sprite.Sprite):
    def __init__(self):
        super().__init__()
        self.image = load_image("spitfire.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((50, 30), pygame.SRCALPHA)
            pygame.draw.polygon(self.image, BLUE, [(0, 15), (40, 0), (50, 15), (40, 30)])

        self.rect = self.image.get_rect()
        self.rect.centerx = SCREEN_WIDTH // 2
        self.rect.bottom = SCREEN_HEIGHT - 50  # Posunuto výše, aby nepřekrývalo letiště
        self.speed = 5
        self.health = 100
        self.max_health = 100
        self.shoot_delay = 250  # ms
        self.last_shot = pygame.time.get_ticks()
        self.lives = 3
        self.invulnerable = False
        self.invulnerable_timer = 0
        self.score = 0
        self.weapon_level = 1  # Úroveň zbraně (max 3)
        self.max_weapon_level = 3

        # Animace a efekty
        self.engine_smoke_timer = 0
        self.blink_timer = 0
        self.visible = True
        self.combo_count = 0
        self.combo_timer = 0
        self.last_kill_time = 0

        # Štít
        self.shield_active = False
        self.shield_timer = 0
        self.shield_duration = 10000  # 10 sekund

        # Zvuky
        self.shoot_sound = load_sound("shoot.wav")
        self.hit_sound = load_sound("hit.wav")
        self.powerup_sound = load_sound("powerup.wav")

    def update(self, game_state="flying"):
        # Pohyb hráče - pouze pokud není ve vzletu nebo přistání
        if game_state == "flying":
            keys = pygame.key.get_pressed()
            if keys[pygame.K_LEFT]:
                self.rect.x -= self.speed
            if keys[pygame.K_RIGHT]:
                self.rect.x += self.speed
            if keys[pygame.K_UP]:
                self.rect.y -= self.speed
            if keys[pygame.K_DOWN]:
                self.rect.y += self.speed

        # Omezení pohybu na obrazovku
        if self.rect.left < 0:
            self.rect.left = 0
        if self.rect.right > SCREEN_WIDTH:
            self.rect.right = SCREEN_WIDTH
        if self.rect.top < 0:
            self.rect.top = 0
        if self.rect.bottom > SCREEN_HEIGHT:
            self.rect.bottom = SCREEN_HEIGHT

        # Kontrola invulnerability a blikání
        if self.invulnerable:
            current_time = pygame.time.get_ticks()
            if current_time - self.invulnerable_timer > 3000:  # 3 sekundy
                self.invulnerable = False
                self.visible = True
            else:
                # Blikání během neporazitelnosti
                self.blink_timer += 1
                if self.blink_timer > 5:
                    self.visible = not self.visible
                    self.blink_timer = 0
        else:
            self.visible = True

        # Generování kouře z motorů
        self.engine_smoke_timer += 1
        if self.engine_smoke_timer > 3:  # Každé 3 snímky
            if 'all_sprites' in globals():
                smoke = EngineSmoke(self.rect.centerx, self.rect.bottom - 5)
                all_sprites.add(smoke)
            self.engine_smoke_timer = 0

        # Aktualizace combo systému
        current_time = pygame.time.get_ticks()
        if current_time - self.last_kill_time > 3000:  # 3 sekundy bez zabití
            self.combo_count = 0

        # Aktualizace štítu
        if self.shield_active:
            if current_time - self.shield_timer > self.shield_duration:
                self.shield_active = False

    def shoot(self):
        now = pygame.time.get_ticks()
        if now - self.last_shot > self.shoot_delay:
            self.last_shot = now

            # Různé typy střelby podle úrovně zbraně
            if self.weapon_level == 1:
                # Základní střelba - jedna střela
                bullet = Bullet(self.rect.centerx, self.rect.top)
                all_sprites.add(bullet)
                bullets.add(bullet)
            elif self.weapon_level == 2:
                # Dvojitá střelba - dvě střely vedle sebe
                bullet1 = Bullet(self.rect.left + 10, self.rect.top)
                bullet2 = Bullet(self.rect.right - 10, self.rect.top)
                all_sprites.add(bullet1, bullet2)
                bullets.add(bullet1, bullet2)
            elif self.weapon_level >= 3:
                # Trojitá střelba - tři střely (jedna vpředu, dvě po stranách)
                bullet1 = Bullet(self.rect.centerx, self.rect.top)
                bullet2 = Bullet(self.rect.left + 5, self.rect.top + 10)
                bullet3 = Bullet(self.rect.right - 5, self.rect.top + 10)
                all_sprites.add(bullet1, bullet2, bullet3)
                bullets.add(bullet1, bullet2, bullet3)

            self.shoot_sound.play()

    def upgrade_weapon(self):
        if self.weapon_level < self.max_weapon_level:
            self.weapon_level += 1
            self.powerup_sound.play()
            return True
        return False

    def add_kill(self):
        """Přidá zabití do combo systému"""
        current_time = pygame.time.get_ticks()
        if current_time - self.last_kill_time < 3000:  # Pokud je to do 3 sekund
            self.combo_count += 1
        else:
            self.combo_count = 1
        self.last_kill_time = current_time

        # Bonus za combo
        if self.combo_count >= 5:
            bonus = self.combo_count * 10
            self.score += bonus
            if 'all_sprites' in globals():
                combo_text = FloatingText(f"COMBO x{self.combo_count}! +{bonus}",
                                        self.rect.centerx, self.rect.top - 20, GOLD)
                all_sprites.add(combo_text)

    def activate_shield(self):
        """Aktivuje štít"""
        self.shield_active = True
        self.shield_timer = pygame.time.get_ticks()
        self.powerup_sound.play()

    def draw(self, surface):
        """Vlastní vykreslení s podporou blikání a štítu"""
        if self.visible:
            surface.blit(self.image, self.rect)

            # Vykreslení štítu
            if self.shield_active:
                shield_radius = 35
                shield_alpha = int(100 + 50 * math.sin(pygame.time.get_ticks() * 0.01))
                shield_surface = pygame.Surface((shield_radius * 2, shield_radius * 2), pygame.SRCALPHA)
                pygame.draw.circle(shield_surface, (*CYAN, shield_alpha), (shield_radius, shield_radius), shield_radius, 3)
                surface.blit(shield_surface, (self.rect.centerx - shield_radius, self.rect.centery - shield_radius))

    def hit(self, damage):
        if not self.invulnerable:
            # Kontrola štítu
            if self.shield_active:
                self.shield_active = False  # Štít se zničí po jednom zásahu
                add_screen_shake(2, 5)
                # Vizuální efekt zničení štítu
                if 'all_sprites' in globals():
                    for i in range(8):
                        spark = HitSpark(self.rect.centerx + random.randint(-20, 20),
                                       self.rect.centery + random.randint(-20, 20))
                        all_sprites.add(spark)
                return False  # Štít absorboval zásah

            self.health -= damage
            self.hit_sound.play()
            add_screen_shake(5, 10)

            if self.health <= 0:
                self.lives -= 1
                if self.lives > 0:
                    self.health = self.max_health
                    self.invulnerable = True
                    self.invulnerable_timer = pygame.time.get_ticks()
                else:
                    player_rect = self.rect.copy()  # Uložíme pozici před kill()
                    self.kill()
                    return game_over(self.score, player_rect)  # Vrátíme výsledek game_over
        return False  # Pokračujeme ve hře

# Třída pro střely hráče
class Bullet(pygame.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        self.image = load_image("bullet.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((4, 10), pygame.SRCALPHA)
            pygame.draw.rect(self.image, YELLOW, (0, 0, 4, 10))

        self.rect = self.image.get_rect()
        self.rect.centerx = x
        self.rect.bottom = y
        self.speed = -10  # Záporná hodnota, protože střela letí nahoru

    def update(self):
        self.rect.y += self.speed
        # Odstranění střely, když opustí obrazovku
        if self.rect.bottom < 0:
            self.kill()

# Třída pro střely nepřátel
class EnemyBullet(pygame.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        self.image = load_image("enemy_bullet.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((4, 10), pygame.SRCALPHA)
            pygame.draw.rect(self.image, RED, (0, 0, 4, 10))

        self.rect = self.image.get_rect()
        self.rect.centerx = x
        self.rect.top = y
        self.speed = 8  # Kladná hodnota, protože střela letí dolů

    def update(self):
        self.rect.y += self.speed
        # Odstranění střely, když opustí obrazovku
        if self.rect.top > SCREEN_HEIGHT:
            self.kill()

# Třída pro nepřátele
class Enemy(pygame.sprite.Sprite):
    def __init__(self, enemy_type="regular"):
        super().__init__()
        self.enemy_type = enemy_type

        if enemy_type == "boss":
            self.image = load_image("boss.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((100, 80), pygame.SRCALPHA)
                pygame.draw.polygon(self.image, RED, [(0, 40), (30, 0), (70, 0), (100, 40), (70, 80), (30, 80)])
            self.health = 100
            self.speed = 2
            self.score_value = 500
            self.shoot_chance = 0.05  # 5% šance na výstřel v každém snímku
            self.move_direction = 1  # 1 = doprava, -1 = doleva
        else:
            self.image = load_image("enemy.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((40, 30), pygame.SRCALPHA)
                pygame.draw.polygon(self.image, RED, [(0, 15), (30, 0), (40, 15), (30, 30)])
            self.health = 20
            self.speed = 3
            self.score_value = 50
            self.shoot_chance = 0.01  # 1% šance na výstřel v každém snímku

        self.rect = self.image.get_rect()
        self.rect.x = random.randrange(SCREEN_WIDTH - self.rect.width)
        self.rect.y = random.randrange(-100, -40)

        # Zvuky
        self.explosion_sound = load_sound("explosion.wav")
        self.shoot_sound = load_sound("shoot.wav")  # Použijeme stejný zvuk jako hráč

    def update(self):
        # Speciální pohyb pro bosse
        if self.enemy_type == "boss":
            # Pohyb zleva doprava
            self.rect.x += self.speed * self.move_direction

            # Změna směru při dosažení okraje
            if self.rect.right > SCREEN_WIDTH or self.rect.left < 0:
                self.move_direction *= -1
        else:
            # Běžný pohyb dolů pro normální nepřátele
            self.rect.y += self.speed
            # Pokud nepřítel opustí obrazovku, vrátí se nahoru
            if self.rect.top > SCREEN_HEIGHT:
                self.rect.x = random.randrange(SCREEN_WIDTH - self.rect.width)
                self.rect.y = random.randrange(-100, -40)

        # Střelba
        self.shoot()

    def shoot(self):
        # Náhodná šance na výstřel
        if random.random() < self.shoot_chance:
            bullet = EnemyBullet(self.rect.centerx, self.rect.bottom)
            all_sprites.add(bullet)
            enemy_bullets.add(bullet)
            self.shoot_sound.play()

    def hit(self, damage):
        self.health -= damage

        # Efekt jisker při zásahu
        if 'all_sprites' in globals():
            for i in range(3):
                spark = HitSpark(self.rect.centerx + random.randint(-10, 10),
                               self.rect.centery + random.randint(-10, 10))
                all_sprites.add(spark)

        if self.health <= 0:
            self.explosion_sound.play()
            explosion = Explosion(self.rect.center)
            if 'all_sprites' in globals():
                all_sprites.add(explosion)

            # Screen shake při zničení
            if self.enemy_type == "boss":
                add_screen_shake(8, 20)
            else:
                add_screen_shake(3, 10)

            self.kill()
            return self.score_value
        return 0

# Třída pro cíle na zemi
class GroundTarget(pygame.sprite.Sprite):
    def __init__(self, x, target_type="building"):
        super().__init__()
        self.target_type = target_type

        if target_type == "building":
            self.image = load_image("building.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((60, 40), pygame.SRCALPHA)
                pygame.draw.rect(self.image, GRAY, (0, 0, 60, 40))
                pygame.draw.polygon(self.image, RED, [(10, 0), (50, 0), (30, -20)])
            self.health = 30
            self.score_value = 100
        elif target_type == "tank":
            self.image = load_image("tank.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((40, 20), pygame.SRCALPHA)
                pygame.draw.rect(self.image, GREEN, (0, 5, 40, 15))
                pygame.draw.rect(self.image, GREEN, (10, 0, 20, 5))
            self.health = 15
            self.score_value = 75

        self.rect = self.image.get_rect()
        self.rect.x = x
        self.rect.bottom = SCREEN_HEIGHT - 10

        # Zvuky
        self.explosion_sound = load_sound("explosion.wav")

    def update(self):
        # Pohyb s pozadím
        self.rect.y += 1
        if self.rect.top > SCREEN_HEIGHT:
            self.kill()

    def hit(self, damage):
        self.health -= damage
        if self.health <= 0:
            self.explosion_sound.play()
            explosion = Explosion(self.rect.center)
            all_sprites.add(explosion)
            self.kill()
            return self.score_value
        return 0

# Třída pro spojence
class Ally(pygame.sprite.Sprite):
    def __init__(self, x):
        super().__init__()
        self.image = load_image("ally.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((20, 30), pygame.SRCALPHA)
            pygame.draw.rect(self.image, BLUE, (5, 10, 10, 20))
            pygame.draw.circle(self.image, BLUE, (10, 5), 5)

        self.rect = self.image.get_rect()
        self.rect.x = x
        self.rect.bottom = SCREEN_HEIGHT - 10
        self.health = 10

        # Zvuky
        self.hit_sound = load_sound("ally_hit.wav")

    def update(self):
        # Pohyb s pozadím
        self.rect.y += 1
        if self.rect.top > SCREEN_HEIGHT:
            self.kill()

    def hit(self, damage):
        self.health -= damage
        self.hit_sound.play()
        if self.health <= 0:
            self.kill()
            return -100  # Penalizace za ztrátu spojence
        return 0

# Třída pro plovoucí text (např. získané body, vylepšení)
class FloatingText(pygame.sprite.Sprite):
    def __init__(self, text, x, y, color=WHITE):
        super().__init__()
        self.font = pygame.font.Font(None, 24)
        self.text = text
        self.color = color
        self.image = self.font.render(text, True, color)
        self.rect = self.image.get_rect()
        self.rect.center = (x, y)
        self.timer = 0
        self.alpha = 255  # Průhlednost
        self.y_speed = -1  # Pohyb nahoru

    def update(self):
        self.timer += 1
        self.rect.y += self.y_speed

        # Postupné mizení textu
        if self.timer > 30:  # Po 30 snímcích začne mizet
            self.alpha -= 8
            if self.alpha <= 0:
                self.kill()
            else:
                # Vytvoření nového obrázku s aktuální průhledností
                self.image = self.font.render(self.text, True, self.color)
                self.image.set_alpha(self.alpha)

# Třída pro částice při vylepšení zbraně
class WeaponUpgradeParticle(pygame.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        self.image = pygame.Surface((5, 5), pygame.SRCALPHA)
        self.color = random.choice([YELLOW, WHITE, GOLD])
        pygame.draw.circle(self.image, self.color, (2, 2), 2)
        self.rect = self.image.get_rect()
        self.rect.center = (x, y)
        self.vx = random.uniform(-3, 3)
        self.vy = random.uniform(-3, 3)
        self.timer = 0

    def update(self):
        self.rect.x += self.vx
        self.rect.y += self.vy
        self.timer += 1
        if self.timer > 30:
            self.kill()

# Třída pro kouř z motorů
class EngineSmoke(pygame.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        size = random.randint(3, 8)
        self.image = pygame.Surface((size, size), pygame.SRCALPHA)
        alpha = random.randint(50, 150)
        color = (*GRAY, alpha)
        pygame.draw.circle(self.image, color[:3], (size//2, size//2), size//2)
        self.image.set_alpha(alpha)
        self.rect = self.image.get_rect()
        self.rect.center = (x + random.randint(-5, 5), y)
        self.vy = random.uniform(1, 3)
        self.vx = random.uniform(-0.5, 0.5)
        self.life = random.randint(30, 60)
        self.timer = 0

    def update(self):
        self.rect.y += self.vy
        self.rect.x += self.vx
        self.timer += 1

        # Postupné mizení
        alpha = max(0, 150 - (self.timer * 3))
        self.image.set_alpha(alpha)

        if self.timer > self.life or alpha <= 0:
            self.kill()

# Třída pro jiskry při zásahu
class HitSpark(pygame.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        self.image = pygame.Surface((3, 3), pygame.SRCALPHA)
        self.color = random.choice([YELLOW, ORANGE, WHITE])
        pygame.draw.circle(self.image, self.color, (1, 1), 1)
        self.rect = self.image.get_rect()
        self.rect.center = (x, y)
        self.vx = random.uniform(-5, 5)
        self.vy = random.uniform(-5, 5)
        self.timer = 0
        self.gravity = 0.2

    def update(self):
        self.rect.x += self.vx
        self.rect.y += self.vy
        self.vy += self.gravity  # Gravitace
        self.timer += 1
        if self.timer > 20:
            self.kill()

# Třída pro hvězdy v pozadí
class Star(pygame.sprite.Sprite):
    def __init__(self):
        super().__init__()
        size = random.randint(1, 3)
        self.image = pygame.Surface((size, size), pygame.SRCALPHA)
        brightness = random.randint(100, 255)
        color = (brightness, brightness, brightness)
        pygame.draw.circle(self.image, color, (size//2, size//2), size//2)
        self.rect = self.image.get_rect()
        self.rect.x = random.randint(0, SCREEN_WIDTH)
        self.rect.y = random.randint(-SCREEN_HEIGHT, 0)
        self.speed = random.uniform(0.5, 2)
        self.twinkle_timer = random.randint(0, 60)
        self.original_alpha = brightness

    def update(self):
        self.rect.y += self.speed
        if self.rect.top > SCREEN_HEIGHT:
            self.rect.y = random.randint(-50, -10)
            self.rect.x = random.randint(0, SCREEN_WIDTH)

        # Blikání hvězd
        self.twinkle_timer += 1
        if self.twinkle_timer > 60:
            self.twinkle_timer = 0
            alpha = random.randint(50, self.original_alpha)
            self.image.set_alpha(alpha)

# Třída pro výbuchy
class Explosion(pygame.sprite.Sprite):
    def __init__(self, center):
        super().__init__()
        self.image = load_image("explosion1.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((50, 50), pygame.SRCALPHA)
            pygame.draw.circle(self.image, YELLOW, (25, 25), 25)

        self.rect = self.image.get_rect()
        self.rect.center = center
        self.frame = 0
        self.frame_rate = 50  # ms
        self.last_update = pygame.time.get_ticks()

        # Animace výbuchu
        self.explosion_anim = []
        for i in range(9):
            img = load_image(f"explosion{i+1}.png")
            if img.get_width() == 50:  # Pokud je to placeholder
                img = pygame.Surface((50, 50), pygame.SRCALPHA)
                size = 25 - i * 2
                if size > 0:
                    pygame.draw.circle(img, YELLOW, (25, 25), size)
                    pygame.draw.circle(img, RED, (25, 25), size - 5 if size > 5 else 0)
            self.explosion_anim.append(img)

    def update(self):
        now = pygame.time.get_ticks()
        if now - self.last_update > self.frame_rate:
            self.last_update = now
            self.frame += 1
            if self.frame == len(self.explosion_anim):
                self.kill()
            else:
                center = self.rect.center
                self.image = self.explosion_anim[self.frame]
                self.rect = self.image.get_rect()
                self.rect.center = center

# Třída pro letiště (start a cíl)
class Airfield(pygame.sprite.Sprite):
    def __init__(self, is_start=True):
        super().__init__()
        self.is_start = is_start
        self.image = load_image("airfield.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((200, 30), pygame.SRCALPHA)  # Tenčí letiště
            pygame.draw.rect(self.image, GRAY, (0, 0, 200, 30))
            for i in range(5):
                pygame.draw.rect(self.image, WHITE, (i * 40 + 10, 12, 20, 5))

        self.rect = self.image.get_rect()
        if is_start:
            self.rect.centerx = SCREEN_WIDTH // 2
            self.rect.top = SCREEN_HEIGHT - 30  # Letiště je na spodku obrazovky, ale nezabírá moc místa
        else:
            self.rect.centerx = SCREEN_WIDTH // 2
            self.rect.top = -self.rect.height  # Končí mimo obrazovku nahoře

    def update(self):
        # Pohyb s pozadím
        if 'background_speed' in globals() and background_speed > 0:
            if self.is_start:
                # Startovní letiště se pohybuje s pozadím dolů
                self.rect.y += background_speed
                if self.rect.top > SCREEN_HEIGHT:
                    self.kill()
            else:
                # Cílové letiště se pohybuje dolů
                self.rect.y += background_speed
                if self.rect.top > SCREEN_HEIGHT - 100:
                    # Zastavit pohyb, když je letiště viditelné
                    self.rect.top = SCREEN_HEIGHT - 100

# Třída pro power-upy
class PowerUp(pygame.sprite.Sprite):
    def __init__(self, powerup_type):
        super().__init__()
        self.powerup_type = powerup_type

        if powerup_type == "health":
            self.image = load_image("health_powerup.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((30, 30), pygame.SRCALPHA)
                pygame.draw.circle(self.image, RED, (15, 15), 15)
                pygame.draw.rect(self.image, WHITE, (10, 5, 10, 20))
                pygame.draw.rect(self.image, WHITE, (5, 10, 20, 10))
        elif powerup_type == "weapon":
            self.image = load_image("weapon_powerup.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((30, 30), pygame.SRCALPHA)
                pygame.draw.circle(self.image, YELLOW, (15, 15), 15)
                pygame.draw.polygon(self.image, BLACK, [(15, 5), (25, 25), (15, 20), (5, 25)])
        elif powerup_type == "shield":
            self.image = pygame.Surface((30, 30), pygame.SRCALPHA)
            pygame.draw.circle(self.image, CYAN, (15, 15), 15)
            pygame.draw.circle(self.image, BLUE, (15, 15), 10)
            pygame.draw.circle(self.image, WHITE, (15, 15), 5)

        self.rect = self.image.get_rect()
        self.rect.x = random.randrange(SCREEN_WIDTH - self.rect.width)
        self.rect.y = random.randrange(-100, -40)
        self.speed = 3
        self.rotation_angle = 0  # Pro rotaci power-upu
        self.original_image = self.image.copy()  # Uložení původního obrázku pro rotaci

        # Zvuky
        self.pickup_sound = load_sound("powerup.wav")

    def update(self):
        self.rect.y += self.speed

        # Rotace power-upu pro lepší viditelnost
        self.rotation_angle = (self.rotation_angle + 2) % 360
        self.image = pygame.transform.rotate(self.original_image, self.rotation_angle)
        old_center = self.rect.center
        self.rect = self.image.get_rect()
        self.rect.center = old_center

        # Pokud power-up opustí obrazovku, odstraní se
        if self.rect.top > SCREEN_HEIGHT:
            self.kill()

# Funkce pro vykreslení textu
def draw_text(surface, text, size, x, y, color=WHITE):
    font = pygame.font.Font(None, size)
    text_surface = font.render(text, True, color)
    text_rect = text_surface.get_rect()
    text_rect.midtop = (x, y)
    surface.blit(text_surface, text_rect)

# Funkce pro vykreslení vylepšeného ukazatele zdraví
def draw_health_bar(surface, x, y, health, max_health, width=100, height=10):
    if health < 0:
        health = 0
    fill = (health / max_health) * width

    # Pozadí health baru
    bg_rect = pygame.Rect(x, y, width, height)
    pygame.draw.rect(surface, (50, 50, 50), bg_rect)

    # Barevný gradient podle zdraví
    health_ratio = health / max_health
    if health_ratio > 0.6:
        color = GREEN
    elif health_ratio > 0.3:
        color = YELLOW
    else:
        color = RED

    # Vyplněná část
    if fill > 0:
        fill_rect = pygame.Rect(x, y, fill, height)
        pygame.draw.rect(surface, color, fill_rect)

        # Lesk efekt
        gloss_rect = pygame.Rect(x, y, fill, height // 2)
        gloss_color = tuple(min(255, c + 50) for c in color)
        pygame.draw.rect(surface, gloss_color, gloss_rect)

    # Obrys
    outline_rect = pygame.Rect(x, y, width, height)
    pygame.draw.rect(surface, WHITE, outline_rect, 2)

# Funkce pro vykreslení životů
def draw_lives(surface, x, y, lives, img=None):
    if img is None:
        img = pygame.Surface((20, 20), pygame.SRCALPHA)
        pygame.draw.polygon(img, BLUE, [(0, 10), (10, 0), (20, 10), (10, 20)])

    for i in range(lives):
        img_rect = img.get_rect()
        img_rect.x = x + 30 * i
        img_rect.y = y
        surface.blit(img, img_rect)

# Funkce pro zobrazení vylepšené úvodní obrazovky
def show_start_screen():
    # Vytvoření hvězd pro úvodní obrazovku
    menu_stars = []
    for i in range(30):
        star = Star()
        menu_stars.append(star)

    waiting = True
    title_pulse = 0

    while waiting:
        clock.tick(FPS)

        # Aktualizace hvězd
        for star in menu_stars:
            star.update()

        # Vykreslení pozadí
        screen.fill(BLACK)

        # Vykreslení hvězd
        for star in menu_stars:
            screen.blit(star.image, star.rect)

        # Pulsující titulek
        title_pulse += 0.1
        title_size = int(64 + math.sin(title_pulse) * 8)

        # Stín titulku
        draw_text(screen, "SPITFIRE", title_size, SCREEN_WIDTH // 2 + 3, SCREEN_HEIGHT // 4 + 3, GRAY)
        # Hlavní titulek
        draw_text(screen, "SPITFIRE", title_size, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4, GOLD)

        # Ovládání
        draw_text(screen, "🎮 OVLÁDÁNÍ 🎮", 28, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 - 40, CYAN)
        draw_text(screen, "Šipky: Pohyb letadla", 20, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
        draw_text(screen, "Mezerník: Střelba", 20, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 25)
        draw_text(screen, "P: Pauza", 20, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 50)

        # Pomalejší blikání textu pro start
        if int(title_pulse * 0.8) % 2:  # Zpomaleno z 2 na 0.8
            draw_text(screen, "Stiskněte libovolnou klávesu pro start", 18, SCREEN_WIDTH // 2, SCREEN_HEIGHT * 3/4, WHITE)

        pygame.display.flip()

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYUP:
                waiting = False

# Funkce pro zobrazení obrazovky konce hry
def game_over(score, player_rect=None):
    global all_sprites

    # Vytvoření efektu výbuchu na místě hráče
    if player_rect:
        for i in range(3):  # Sníženo na 3 výbuchy pro lepší výkon
            explosion = Explosion(player_rect.center)
            if 'all_sprites' in globals():
                all_sprites.add(explosion)
            # Malé zpoždění mezi výbuchy
            pygame.time.delay(50)
            screen.fill(BLACK)
            if 'all_sprites' in globals():
                all_sprites.draw(screen)
            pygame.display.flip()

    # Postupné ztmavení obrazovky
    fade_surface = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
    fade_surface.fill(BLACK)
    for alpha in range(0, 256, 10):  # Rychlejší fade
        fade_surface.set_alpha(alpha)
        screen.blit(fade_surface, (0, 0))
        pygame.display.flip()
        pygame.time.delay(20)

    # Zobrazení vylepšené obrazovky konce hry
    screen.fill(BLACK)

    # Animovaný titulek
    for i in range(3):
        size = 64 - i * 5
        color_intensity = 255 - i * 50
        color = (color_intensity, 0, 0)
        draw_text(screen, "KONEC HRY", size, SCREEN_WIDTH // 2 + i, SCREEN_HEIGHT // 4 + i, color)

    # Hlavní skóre
    draw_text(screen, f"Finální skóre: {score}", 36, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 - 50, GOLD)

    # Zobrazení hodnocení podle skóre s barvami
    if score > 5000:
        rating = "🏆 LETECKÉ ESO! 🏆"
        rating_color = GOLD
    elif score > 3000:
        rating = "⭐ Zkušený pilot ⭐"
        rating_color = SILVER
    elif score > 1000:
        rating = "✈️ Dobrý pilot ✈️"
        rating_color = CYAN
    else:
        rating = "🎯 Začátečník 🎯"
        rating_color = WHITE

    draw_text(screen, rating, 30, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2, rating_color)

    # Statistiky
    draw_text(screen, "📊 STATISTIKY 📊", 24, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 40, LIGHT_BLUE)
    if hasattr(player, 'combo_count') and player.combo_count > 0:
        draw_text(screen, f"Nejvyšší combo: {player.combo_count}x", 18, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 70, YELLOW)

    # Blikající restart text
    import time
    if int(time.time() * 2) % 2:
        draw_text(screen, "Stiskněte libovolnou klávesu pro restart", 18, SCREEN_WIDTH // 2, SCREEN_HEIGHT * 3/4, WHITE)

    pygame.display.flip()

    # Čekání na stisk klávesy
    waiting = True
    while waiting:
        clock.tick(FPS)
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYUP:
                waiting = False
                return True  # Signalizuje restart

# Funkce pro zobrazení obrazovky dokončení úrovně
def level_complete_screen():
    screen.fill(BLACK)
    draw_text(screen, "ÚROVEŇ DOKONČENA!", 64, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4)
    draw_text(screen, f"Skóre: {player.score}", 36, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
    draw_text(screen, "Stiskněte libovolnou klávesu pro pokračování", 18, SCREEN_WIDTH // 2, SCREEN_HEIGHT * 3/4)
    pygame.display.flip()
    waiting = True
    while waiting:
        clock.tick(FPS)
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYUP:
                waiting = False

# Funkce pro zobrazení pauzy
def show_pause_screen():
    # Vytvoření poloprůhledného overlay
    pause_surface = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
    pause_surface.set_alpha(128)
    pause_surface.fill(BLACK)
    screen.blit(pause_surface, (0, 0))

    # Zobrazení textu pauzy
    draw_text(screen, "PAUZA", 64, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 - 50)
    draw_text(screen, "Stiskněte P pro pokračování", 24, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 20)
    draw_text(screen, "ESC pro ukončení hry", 18, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 60)
    pygame.display.flip()

    # Čekání na stisk klávesy
    waiting = True
    while waiting:
        clock.tick(FPS)
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_p:
                    waiting = False
                elif event.key == pygame.K_ESCAPE:
                    pygame.quit()
                    sys.exit()

# Funkce pro vytvoření jednoho nepřítele bez překrývání
def spawn_single_enemy():
    # Vytvoření nepřítele
    enemy = Enemy()

    # Definice minimální vzdálenosti mezi nepřáteli
    min_distance = 100  # Výrazně zvýšená minimální vzdálenost

    # Rozdělení horní části obrazovky na sloupce
    num_columns = 5
    column_width = SCREEN_WIDTH // num_columns

    # Výběr náhodného sloupce
    column = random.randint(0, num_columns - 1)

    # Výpočet x-pozice v rámci sloupce (s okraji)
    margin = 20
    min_x = column * column_width + margin
    max_x = (column + 1) * column_width - enemy.rect.width - margin

    if max_x <= min_x:
        max_x = min_x + 10

    enemy.rect.x = random.randint(min_x, max_x)
    enemy.rect.y = random.randint(-200, -50)

    # Kontrola překrývání s existujícími nepřáteli
    overlap = True
    max_attempts = 30  # Ještě více pokusů
    attempts = 0

    while overlap and attempts < max_attempts:
        overlap = False

        # Zkusíme různé y-pozice (x-pozice zůstává v rámci sloupce)
        enemy.rect.y = random.randint(-300 - attempts*20, -50)  # S každým pokusem jdeme výše

        # Kontrola minimální vzdálenosti od ostatních nepřátel
        for existing_enemy in enemies:
            # Vypočítáme vzdálenost mezi středy nepřátel
            dx = enemy.rect.centerx - existing_enemy.rect.centerx
            dy = enemy.rect.centery - existing_enemy.rect.centery
            distance = math.sqrt(dx*dx + dy*dy)

            # Pokud jsou příliš blízko, zkusíme znovu
            if distance < min_distance:
                overlap = True
                attempts += 1
                break

    # Pokud se nepodařilo najít místo bez překrývání, umístíme nepřítele výrazně výše
    if overlap:
        enemy.rect.y = random.randint(-500, -300)

    # Přidání nepřítele do skupin
    all_sprites.add(enemy)
    enemies.add(enemy)
    return enemy

# Funkce pro vytvoření počáteční skupiny nepřátel
def spawn_enemies(count):
    for i in range(count):
        spawn_single_enemy()

# Funkce pro vytvoření pozemních cílů bez překrývání
def spawn_ground_targets(count):
    for i in range(count):
        target_type = random.choice(["building", "tank"])
        target = GroundTarget(0, target_type)  # Dočasná x-pozice

        # Kontrola překrývání s existujícími cíli
        overlap = True
        max_attempts = 10
        attempts = 0

        while overlap and attempts < max_attempts:
            overlap = False
            x = random.randrange(SCREEN_WIDTH - target.rect.width)
            target.rect.x = x

            for existing_target in ground_targets:
                if pygame.sprite.collide_rect(target, existing_target):
                    overlap = True
                    attempts += 1
                    break

        all_sprites.add(target)
        ground_targets.add(target)

# Funkce pro vytvoření spojenců bez překrývání
def spawn_allies(count):
    for i in range(count):
        ally = Ally(0)  # Dočasná x-pozice

        # Kontrola překrývání s existujícími spojenci
        overlap = True
        max_attempts = 10
        attempts = 0

        while overlap and attempts < max_attempts:
            overlap = False
            x = random.randrange(SCREEN_WIDTH - ally.rect.width)
            ally.rect.x = x

            for existing_ally in allies:
                if pygame.sprite.collide_rect(ally, existing_ally):
                    overlap = True
                    attempts += 1
                    break

        all_sprites.add(ally)
        allies.add(ally)

# Funkce pro vytvoření power-upů
def spawn_powerup(powerup_type=None):
    if powerup_type is None:
        powerup_type = random.choice(["health", "weapon"])

    powerup = PowerUp(powerup_type)
    all_sprites.add(powerup)
    powerups.add(powerup)

# Funkce pro vytvoření bosse na konci úrovně
def spawn_boss():
    boss = Enemy(enemy_type="boss")
    # Umístění bosse na střed obrazovky nahoře
    boss.rect.centerx = SCREEN_WIDTH // 2
    boss.rect.top = 50
    all_sprites.add(boss)
    enemies.add(boss)

    # Vytvoření cílového letiště
    end_airfield = Airfield(is_start=False)
    all_sprites.add(end_airfield)

# Hlavní herní funkce
def main():
    global all_sprites, bullets, enemy_bullets, enemies, ground_targets, allies, powerups, player, background_speed

    # Vytvoření skupin spritů
    all_sprites = pygame.sprite.Group()
    bullets = pygame.sprite.Group()
    enemy_bullets = pygame.sprite.Group()
    enemies = pygame.sprite.Group()
    ground_targets = pygame.sprite.Group()
    allies = pygame.sprite.Group()
    powerups = pygame.sprite.Group()
    stars = pygame.sprite.Group()

    # Vytvoření hvězd v pozadí
    for i in range(50):
        star = Star()
        stars.add(star)

    # Vytvoření startovního letiště - nejprve letiště, aby bylo pod letadlem
    start_airfield = Airfield(is_start=True)
    all_sprites.add(start_airfield)

    # Vytvoření hráče - začíná na letišti
    player = Player()
    # Umístíme hráče na střed ranveje letiště
    player.rect.centerx = start_airfield.rect.centerx
    player.rect.bottom = start_airfield.rect.centery + 20  # Na ranveji
    all_sprites.add(player)

    # Herní stav pro start
    game_state = "takeoff"  # takeoff, flying, landing
    takeoff_timer = 0

    # Vytvoření pozadí
    background = ScrollingBackground("background.png", 1)
    background_speed = 1  # Výchozí rychlost pohybu pozadí

    # Herní proměnné
    level = 1
    level_complete = False
    boss_active = False
    enemy_spawn_delay = 2000  # ms mezi generováním nepřátel
    last_enemy_spawn = pygame.time.get_ticks()
    max_enemies = 10  # Maximální počet nepřátel na obrazovce
    level_start_score = 0  # Skóre na začátku úrovně
    paused = False  # Stav pauzy

    # Načtení hudby
    try:
        pygame.mixer.music.load(os.path.join(music_dir, "game_music.mp3"))
        pygame.mixer.music.set_volume(0.4)
        pygame.mixer.music.play(loops=-1)
    except:
        try:
            pygame.mixer.music.load(os.path.join(music_dir, "game_music.wav"))
            pygame.mixer.music.set_volume(0.4)
            pygame.mixer.music.play(loops=-1)
        except:
            print("Hudební soubor nebyl nalezen. Hra poběží bez hudby.")

    # Zobrazení úvodní obrazovky
    show_start_screen()

    # Vytvoření počátečních cílů pro první úroveň
    spawn_ground_targets(3)
    spawn_allies(2)

    # Hlavní herní smyčka
    running = True
    while running:
        # Udržování správné rychlosti
        clock.tick(FPS)

        # Zpracování událostí
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_p:
                    paused = not paused
                    if paused:
                        show_pause_screen()
                elif event.key == pygame.K_SPACE and not paused and game_state == "flying":
                    player.shoot()

        # Pokud je hra pozastavena, přeskočíme aktualizace
        if paused:
            continue

        # Logika vzletu
        if game_state == "takeoff":
            takeoff_timer += 1
            if takeoff_timer < 120:  # 2 sekundy při 60 FPS
                # Hráč se nemůže pohybovat během vzletu
                player.rect.y -= 1  # Pomalý vzlet
                # Zobrazení textu vzletu
                draw_text(screen, "VZLET...", 36, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2, YELLOW)
            else:
                game_state = "flying"
                # Nyní může hráč normálně létat

        # Logika přistání po zabití bosse
        elif game_state == "landing":
            # Hráč se automaticky pohybuje k letišti
            for sprite in all_sprites:
                if isinstance(sprite, Airfield) and not sprite.is_start:
                    target_x = sprite.rect.centerx
                    target_y = sprite.rect.centery

                    # Automatické řízení k letišti
                    if abs(player.rect.centerx - target_x) > 5:
                        if player.rect.centerx < target_x:
                            player.rect.x += 2
                        else:
                            player.rect.x -= 2

                    if abs(player.rect.centery - target_y) > 5:
                        if player.rect.centery < target_y:
                            player.rect.y += 2
                        else:
                            player.rect.y -= 2
                    else:
                        # Přistál!
                        game_state = "landed"
                        draw_text(screen, "PŘISTÁNÍ DOKONČENO!", 36, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2, GREEN)

        # Kontrola, zda je boss na obrazovce
        boss_active = False
        for enemy in enemies:
            if enemy.enemy_type == "boss":
                boss_active = True
                break

        # Nastavení rychlosti pozadí podle přítomnosti bosse
        if boss_active:
            background_speed = 0  # Zastavení pohybu pozadí, když je boss na obrazovce
        else:
            background_speed = 1  # Normální rychlost pohybu

        # Aktualizace pozadí
        background.speed = background_speed
        background.update()

        # Aktualizace spritů
        # Aktualizujeme hráče s herním stavem
        player.update(game_state)

        # Aktualizujeme ostatní sprity
        for sprite in all_sprites:
            if sprite != player:
                sprite.update()

        # Průběžné generování nepřátel, pouze během letu
        now = pygame.time.get_ticks()
        if (game_state == "flying" and not boss_active and not level_complete and
            len(enemies) < max_enemies and now - last_enemy_spawn > enemy_spawn_delay):
            spawn_single_enemy()
            last_enemy_spawn = now
            # Postupné zkracování intervalu mezi nepřáteli s vyšší úrovní
            enemy_spawn_delay = max(2000 - (level * 100), 500)  # Minimálně 500ms

        # Kontrola kolizí střel hráče s nepřáteli
        hits = pygame.sprite.groupcollide(enemies, bullets, False, True)
        for enemy, bullet_list in hits.items():
            for bullet in bullet_list:
                score_value = enemy.hit(10)
                if score_value > 0:  # Pokud byl nepřítel zničen
                    player.add_kill()  # Přidáme do combo systému

                    # Pokud byl zabit boss, začneme přistání
                    if enemy.enemy_type == "boss":
                        game_state = "landing"
                        # Vytvoříme cílové letiště
                        end_airfield = Airfield(is_start=False)
                        end_airfield.rect.centerx = SCREEN_WIDTH // 2
                        end_airfield.rect.top = 50  # Nahoře na obrazovce
                        all_sprites.add(end_airfield)

                player.score += score_value

        # Kontrola kolizí střel nepřátel s hráčem
        hits = pygame.sprite.spritecollide(player, enemy_bullets, True)
        for hit in hits:
            player.hit(10)

        # Kontrola kolizí střel s pozemními cíli
        hits = pygame.sprite.groupcollide(ground_targets, bullets, False, True)
        for target, bullet_list in hits.items():
            for bullet in bullet_list:
                score_value = target.hit(10)
                player.score += score_value

        # Kontrola kolizí střel se spojenci (penalizace)
        hits = pygame.sprite.groupcollide(allies, bullets, False, True)
        for ally, bullet_list in hits.items():
            for bullet in bullet_list:
                score_value = ally.hit(10)
                player.score += score_value  # Záporná hodnota

        # Kontrola kolizí hráče s nepřáteli
        hits = pygame.sprite.spritecollide(player, enemies, False)
        for hit in hits:
            restart_game = player.hit(20)
            if restart_game:
                return  # Ukončíme aktuální hru a restartujeme
            hit.hit(100)  # Zničí nepřítele

        # Kontrola kolizí hráče s power-upy
        hits = pygame.sprite.spritecollide(player, powerups, True)
        for hit in hits:
            if hit.powerup_type == "health":
                player.health = min(player.health + 20, player.max_health)
                # Zobrazení textu o získání zdraví
                healing_text = FloatingText(f"+20 HP", player.rect.centerx, player.rect.top, GREEN)
                all_sprites.add(healing_text)
            elif hit.powerup_type == "weapon":
                if player.upgrade_weapon():
                    # Zobrazení textu o vylepšení zbraně
                    upgrade_text = FloatingText(f"Zbraň +{player.weapon_level}", player.rect.centerx, player.rect.top, YELLOW)
                    all_sprites.add(upgrade_text)
                    # Vizuální efekt vylepšení
                    for i in range(10):
                        particle = WeaponUpgradeParticle(player.rect.centerx, player.rect.centery)
                        all_sprites.add(particle)
                else:
                    # Pokud už je zbraň na maximální úrovni, přidáme body
                    player.score += 100
                    score_text = FloatingText("+100 bodů", player.rect.centerx, player.rect.top, WHITE)
                    all_sprites.add(score_text)
            elif hit.powerup_type == "shield":
                if not player.shield_active:
                    player.activate_shield()
                    shield_text = FloatingText("ŠTÍT AKTIVOVÁN!", player.rect.centerx, player.rect.top, CYAN)
                    all_sprites.add(shield_text)
                else:
                    # Pokud už má štít, přidáme body
                    player.score += 150
                    score_text = FloatingText("+150 bodů", player.rect.centerx, player.rect.top, WHITE)
                    all_sprites.add(score_text)

        # Náhodné generování power-upů
        if random.random() < 0.005:  # 0.5% šance v každém snímku
            # Různé typy power-upů podle situace
            available_powerups = ["health"]

            if player.weapon_level < player.max_weapon_level:
                available_powerups.append("weapon")

            if not player.shield_active:
                available_powerups.append("shield")

            # Pokud jsou dostupné všechny typy, přidáme váhy
            if len(available_powerups) > 1:
                powerup_type = random.choice(available_powerups)
            else:
                powerup_type = available_powerups[0]

            powerup = PowerUp(powerup_type)
            all_sprites.add(powerup)
            powerups.add(powerup)

        # Kontrola dokončení úrovně - když je zabito dostatečné množství nepřátel
        # Sledujeme skóre potřebné pro aktuální úroveň
        if not level_complete and not boss_active:
            # Potřebné skóre pro dokončení úrovně (od začátku této úrovně)
            required_score_gain = level * 500
            current_level_score = player.score - level_start_score

            # Pokud hráč dosáhl potřebného skóre v této úrovni, vytvoříme bosse
            if current_level_score >= required_score_gain:
                # Vytvoření bosse
                spawn_boss()
                level_complete = True
                # Informace pro hráče
                boss_text = FloatingText("BOSS!", SCREEN_WIDTH // 2, SCREEN_HEIGHT // 3, RED)
                all_sprites.add(boss_text)

        # Kontrola dokončení přistání
        if game_state == "landed":
            # Čekáme chvilku a pak přecházíme na další úroveň
            pygame.time.delay(2000)  # 2 sekundy pauza

            level += 1
            level_complete_screen()

            # Nastavení pro další úroveň
            for sprite in all_sprites:
                sprite.kill()

            # Nejprve vytvoříme letiště, aby bylo pod letadlem
            start_airfield = Airfield(is_start=True)
            all_sprites.add(start_airfield)

            # Reset hráče - umístíme ho na letiště
            old_score = player.score  # Uložení skóre
            player = Player()  # Vytvoření nového hráče s resetovanými zbraněmi
            player.score = old_score  # Zachování skóre
            player.rect.centerx = start_airfield.rect.centerx
            player.rect.bottom = start_airfield.rect.centery + 20  # Na ranveji
            all_sprites.add(player)

            # Vytvoření cílů pro novou úroveň
            spawn_ground_targets(3 + level)
            spawn_allies(2 + level // 2)

            # Reset herních proměnných
            level_complete = False
            boss_active = False
            enemy_spawn_delay = max(2000 - (level * 100), 500)
            max_enemies = 10 + level  # Zvýšení maximálního počtu nepřátel s úrovní
            level_start_score = player.score  # Uložení skóre na začátku nové úrovně

            # Reset herního stavu pro nový vzlet
            game_state = "takeoff"
            takeoff_timer = 0

        # Aktualizace hvězd
        stars.update()

        # Vykreslení s screen shake
        offset_x, offset_y = get_screen_offset()

        # Vytvoření surface pro vykreslení s offsetem
        if offset_x != 0 or offset_y != 0:
            temp_surface = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
            temp_surface.fill(BLACK)

            # Vykreslení hvězd
            for star in stars:
                temp_surface.blit(star.image, (star.rect.x + offset_x, star.rect.y + offset_y))

            background.draw(temp_surface)

            # Vykreslení všech spritů kromě hráče
            for sprite in all_sprites:
                if sprite != player:
                    temp_surface.blit(sprite.image, (sprite.rect.x + offset_x, sprite.rect.y + offset_y))

            # Vykreslení hráče s vlastní metodou (kvůli blikání)
            if hasattr(player, 'draw'):
                player.draw(temp_surface)
            else:
                temp_surface.blit(player.image, (player.rect.x + offset_x, player.rect.y + offset_y))

            screen.blit(temp_surface, (0, 0))
        else:
            screen.fill(BLACK)

            # Vykreslení hvězd
            for star in stars:
                screen.blit(star.image, star.rect)

            background.draw(screen)

            # Vykreslení všech spritů kromě hráče
            for sprite in all_sprites:
                if sprite != player:
                    screen.blit(sprite.image, sprite.rect)

            # Vykreslení hráče s vlastní metodou (kvůli blikání)
            if hasattr(player, 'draw'):
                player.draw(screen)
            else:
                screen.blit(player.image, player.rect)

        # Vykreslení UI (bez shake efektu)
        draw_text(screen, f"Skóre: {player.score}", 18, SCREEN_WIDTH // 2, 10)
        draw_text(screen, f"Úroveň: {level}", 18, SCREEN_WIDTH - 50, 10)
        draw_health_bar(screen, 5, 5, player.health, player.max_health)
        draw_lives(screen, SCREEN_WIDTH - 100, 30, player.lives)

        # Zobrazení úrovně zbraně
        draw_text(screen, f"Zbraň: {player.weapon_level}", 18, 100, 30)

        # Zobrazení štítu
        if player.shield_active:
            remaining_time = (player.shield_duration - (pygame.time.get_ticks() - player.shield_timer)) // 1000
            draw_text(screen, f"🛡️ Štít: {remaining_time}s", 18, 100, 50, CYAN)

        # Zobrazení combo
        if player.combo_count > 1:
            combo_y = 70 if player.shield_active else 50
            draw_text(screen, f"COMBO x{player.combo_count}", 24, 100, combo_y, GOLD)

        # Zobrazení herního stavu
        if game_state == "takeoff":
            draw_text(screen, "VZLET...", 48, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2, YELLOW)
            draw_text(screen, "Připravte se na misi!", 24, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 60, WHITE)
        elif game_state == "landing":
            draw_text(screen, "PŘISTÁNÍ...", 48, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2, CYAN)
            draw_text(screen, "Navigace k letišti", 24, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 60, WHITE)
        elif game_state == "landed":
            draw_text(screen, "MISE DOKONČENA!", 48, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2, GREEN)
            draw_text(screen, "Příprava na další úroveň...", 24, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 60, WHITE)

        # Aktualizace obrazovky
        pygame.display.flip()

    # Ukončení hry
    pygame.quit()
    sys.exit()

# Spuštění hry s podporou restartu
if __name__ == "__main__":
    while True:
        try:
            main()
        except SystemExit:
            break
        except Exception as e:
            print(f"Chyba ve hře: {e}")
            break