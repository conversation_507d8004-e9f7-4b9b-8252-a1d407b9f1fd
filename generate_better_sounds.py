#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON><PERSON> efektů pro hru Spitfire
"""

import numpy as np
import wave
import os
import math

def save_wav(filename, audio_data, sample_rate=44100):
    """Uloží audio data jako WAV soubor"""
    # Normalizace a konverze na 16-bit
    audio_data = np.clip(audio_data, -1.0, 1.0)
    audio_data = (audio_data * 32767).astype(np.int16)
    
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_data.tobytes())

def generate_smooth_gunfire():
    """Generuje hladký zvuk střelby - ne jako morzeovka"""
    duration = 0.08  # Velmi krátký
    sample_rate = 44100
    
    # Vytvoření základního šumu
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # Komb<PERSON><PERSON> frekvencí pro realistický zvuk střelby
    audio = np.zeros_like(t)
    
    # Vysokofrekvenční "crack"
    crack = np.sin(2 * np.pi * 2000 * t) * 0.3
    audio += crack
    
    # Středofrekvenční "pop"
    pop = np.sin(2 * np.pi * 800 * t) * 0.4
    audio += pop
    
    # Nízká frekvence pro "thump"
    thump = np.sin(2 * np.pi * 120 * t) * 0.3
    audio += thump
    
    # Přidání kontrolovaného šumu
    noise = np.random.uniform(-0.2, 0.2, len(t))
    audio += noise
    
    # Velmi rychlý útlum pro ostrý zvuk
    envelope = np.exp(-t * 50)  # Rychlý útlum
    audio *= envelope
    
    return audio

def generate_realistic_explosion():
    """Generuje realistický zvuk exploze"""
    duration = 1.5
    sample_rate = 44100
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio = np.zeros_like(t)
    
    # Počáteční "boom" - nízké frekvence
    boom_duration = 0.3
    boom_mask = t < boom_duration
    boom = np.sin(2 * np.pi * 40 * t[boom_mask]) * 0.8
    audio[boom_mask] += boom
    
    # Střední frekvence pro "crack"
    crack_duration = 0.5
    crack_mask = t < crack_duration
    crack = np.sin(2 * np.pi * 300 * t[crack_mask]) * 0.5
    audio[crack_mask] += crack
    
    # Šum pro realistický efekt
    noise = np.random.uniform(-0.4, 0.4, len(t))
    audio += noise
    
    # Envelope s pomalým útlumem
    envelope = np.exp(-t * 2)
    audio *= envelope
    
    return audio

def generate_smooth_hit():
    """Generuje hladký zvuk zásahu"""
    duration = 0.2
    sample_rate = 44100
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # Kombinace tónů pro příjemný zvuk
    audio = np.sin(2 * np.pi * 600 * t) * 0.4
    audio += np.sin(2 * np.pi * 900 * t) * 0.3
    
    # Přidání lehkého šumu
    noise = np.random.uniform(-0.1, 0.1, len(t))
    audio += noise
    
    # Hladký útlum
    envelope = np.exp(-t * 15)
    audio *= envelope
    
    return audio

def generate_pleasant_powerup():
    """Generuje příjemný zvuk power-up"""
    duration = 0.8
    sample_rate = 44100
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # Stoupající harmonická sekvence
    audio = np.zeros_like(t)
    
    # Základní tón stoupající
    freq_start = 400
    freq_end = 800
    freq = freq_start + (freq_end - freq_start) * (t / duration)
    audio += np.sin(2 * np.pi * freq * t) * 0.4
    
    # Harmonické
    audio += np.sin(2 * np.pi * freq * 1.5 * t) * 0.2
    audio += np.sin(2 * np.pi * freq * 2 * t) * 0.1
    
    # Hladký envelope
    envelope = np.exp(-t * 1.5) * (1 - t / duration * 0.5)
    audio *= envelope
    
    return audio

def generate_menu_music():
    """Generuje klidnou hudbu pro menu"""
    duration = 10.0  # 10 sekund, bude se opakovat
    sample_rate = 44100
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio = np.zeros_like(t)
    
    # Pentatonická stupnice (C, D, E, G, A)
    notes = [261.63, 293.66, 329.63, 392.00, 440.00]  # C4, D4, E4, G4, A4
    
    # Jednoduchá melodie
    note_duration = 1.0
    for i, note_freq in enumerate([notes[0], notes[2], notes[1], notes[4], notes[3], notes[0]]):
        start_time = i * note_duration
        end_time = start_time + note_duration * 1.5  # Překrývání not
        
        if end_time <= duration:
            note_mask = (t >= start_time) & (t < end_time)
            note_t = t[note_mask] - start_time
            
            # Hlavní tón
            note = np.sin(2 * np.pi * note_freq * note_t) * 0.3
            
            # Harmonické pro bohatší zvuk
            note += np.sin(2 * np.pi * note_freq * 2 * note_t) * 0.1
            note += np.sin(2 * np.pi * note_freq * 3 * note_t) * 0.05
            
            # Envelope pro hladší přechody
            note_envelope = np.exp(-note_t * 0.5)
            note *= note_envelope
            
            audio[note_mask] += note
    
    # Basová linka
    bass_notes = [notes[0] / 2, notes[3] / 2, notes[1] / 2, notes[4] / 2]
    bass_duration = 2.0
    
    for i, bass_freq in enumerate(bass_notes):
        start_time = i * bass_duration
        end_time = start_time + bass_duration
        
        if end_time <= duration:
            bass_mask = (t >= start_time) & (t < end_time)
            bass_t = t[bass_mask] - start_time
            
            bass = np.sin(2 * np.pi * bass_freq * bass_t) * 0.2
            bass_envelope = np.exp(-bass_t * 0.3)
            bass *= bass_envelope
            
            audio[bass_mask] += bass
    
    return audio

def main():
    """Hlavní funkce"""
    print("🎵 Generování lepších zvukových efektů...")
    
    # Vytvoření adresářů
    os.makedirs("assets/sounds", exist_ok=True)
    os.makedirs("assets/music", exist_ok=True)
    
    # Generování lepších zvuků
    print("🔫 Generuji hladký zvuk střelby...")
    gunfire = generate_smooth_gunfire()
    save_wav("assets/sounds/shoot.wav", gunfire)
    save_wav("assets/sounds/gunfire.wav", gunfire)  # Backup
    
    print("💥 Generuji realistickou explozi...")
    explosion = generate_realistic_explosion()
    save_wav("assets/sounds/explosion.wav", explosion)
    
    print("🎯 Generuji hladký zvuk zásahu...")
    hit = generate_smooth_hit()
    save_wav("assets/sounds/hit.wav", hit)
    save_wav("assets/sounds/enemy_hit.wav", hit)
    
    print("⚡ Generuji příjemný power-up...")
    powerup = generate_pleasant_powerup()
    save_wav("assets/sounds/powerup.wav", powerup)
    
    print("🎼 Generuji klidnou hudbu pro menu...")
    menu_music = generate_menu_music()
    save_wav("assets/music/menu_music.wav", menu_music)
    
    # Vytvoření také tichých zvuků pro chybějící efekty
    print("🔇 Generuji pomocné zvuky...")
    
    # Ally hit - jemnější verze hit zvuku
    ally_hit = generate_smooth_hit() * 0.5  # Tišší
    save_wav("assets/sounds/ally_hit.wav", ally_hit)
    
    # Player hit - výraznější
    player_hit = generate_smooth_hit() * 1.2
    save_wav("assets/sounds/player_hit.wav", player_hit)
    
    print("✅ Všechny lepší zvuky vygenerovány!")

if __name__ == "__main__":
    main()
