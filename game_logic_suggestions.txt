NÁVRHY PRO ÚPRAVU HERNÍ LOGIKY SPITFIRE

Na základě vašich připomínek doporučuji následující úpravy v herním kódu:

1. POHYB POZADÍ A OBJEKTŮ:
   - Všechny objekty (leti<PERSON><PERSON><PERSON>, spojenc<PERSON>, budovy) by m<PERSON><PERSON> z<PERSON><PERSON> mimo obrazovku nahoře
   - Objekty by se měly pohybovat dolů společně s pozadím
   - Rychlost pohybu by mě<PERSON> b<PERSON>t stejná pro všechny objekty na zemi

2. NEPŘÁTELÉ:
   - Nepřátelé jsou nyní otočeni správným směrem (letí proti hráči)
   - Nepřátelé by m<PERSON><PERSON> st<PERSON><PERSON> - přidejte kód pro generování střel z nepřátelských letadel
   - Použijte nový asset "enemy_bullet.png" pro střely nepřátel
   - Frekvence střelby by m<PERSON><PERSON> b<PERSON><PERSON> ni<PERSON> než u hráče

3. BOSS:
   - <PERSON><PERSON><PERSON> se objev<PERSON> boss, poz<PERSON><PERSON> by se mě<PERSON> z<PERSON>avit
   - Boss by m<PERSON><PERSON> m<PERSON><PERSON> vlast<PERSON><PERSON> pohybový vzorec (např. pohyb zleva doprava)
   - Boss by měl střílet častěji a případně více střel najednou
   - Boss by měl mít více životů než běžní nepřátelé
   - Po zničení bosse by se měl obraz opět začít pohybovat

4. DRUHÝ LEVEL:
   - Zajistěte, aby se ve druhém levelu generovali nepřátelé
   - Druhý level by měl být obtížnější - více nepřátel, rychlejší střelba
   - Nepřátelé by mohli mít jiné pohybové vzorce

5. HUDBA:
   - Hudba je nyní uložena jako WAV soubor s kopií jako MP3
   - Ujistěte se, že hra načítá hudbu správně
   - Přidejte kód pro opětovné spuštění hudby, když skončí

KONKRÉTNÍ ÚPRAVY KÓDU:

1. Pro střelbu nepřátel přidejte do třídy Enemy metodu shoot():
```python
def shoot(self):
    if random.random() < 0.01:  # 1% šance na výstřel v každém snímku
        bullet = EnemyBullet(self.rect.centerx, self.rect.bottom)
        all_sprites.add(bullet)
        enemy_bullets.add(bullet)
        if shoot_sound:
            shoot_sound.play()
```

2. Pro zastavení pozadí při bossovi:
```python
# V hlavní herní smyčce
if boss_active:
    background_speed = 0
else:
    background_speed = 2  # nebo jakoukoli hodnotu používáte
```

3. Pro pohyb bosse:
```python
def update(self):
    # Pohyb zleva doprava
    self.rect.x += self.speed
    
    # Změna směru při dosažení okraje
    if self.rect.right > WIDTH or self.rect.left < 0:
        self.speed *= -1
        
    # Střelba
    if random.random() < 0.05:  # 5% šance na výstřel
        self.shoot()
```

4. Pro generování nepřátel ve druhém levelu:
```python
# Ujistěte se, že tato část kódu se spouští i pro druhý level
if current_level == 2:
    if random.random() < 0.03:  # Vyšší šance než v prvním levelu
        enemy = Enemy()
        all_sprites.add(enemy)
        enemies.add(enemy)
```

Tyto úpravy by měly vyřešit problémy, které jste identifikoval. Implementujte je do hlavního herního kódu.