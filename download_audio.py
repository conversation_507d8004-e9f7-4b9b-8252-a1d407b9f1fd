#!/usr/bin/env python3
"""
Stahování audio assetů pro hru Spitfire
"""

import urllib.request
import os
import shutil

def download_file(url, filename):
    """Stáhne soubor z URL"""
    try:
        print(f"Stahuji {filename}...")
        urllib.request.urlretrieve(url, filename)
        print(f"✅ Úspěšně staženo: {filename}")
        return True
    except Exception as e:
        print(f"❌ Chyba při stahování {filename}: {e}")
        return False

def main():
    """Hlavní funkce pro stahování audio assetů"""

    # Vytvoření adresářů
    os.makedirs("assets/sounds", exist_ok=True)
    os.makedirs("assets/music", exist_ok=True)

    print("🎵 Stahování audio assetů pro Spitfire...")

    # Seznam audio souborů ke stažení
    audio_files = [
        # Hudba
        {
            "url": "https://opengameart.org/sites/default/files/POL-battle-march-short_0.wav",
            "filename": "assets/music/battle_march.wav",
            "description": "Epic Battle March"
        },

        # Zvukové efekty z OpenGameArt
        {
            "url": "https://opengameart.org/sites/default/files/explosions.7z",
            "filename": "explosions.7z",
            "description": "Explosion sounds"
        },

        # Alternativní přímé odkazy na zvuky
        {
            "url": "https://www.soundjay.com/misc/sounds-1/beep-07a.wav",
            "filename": "assets/sounds/beep.wav",
            "description": "Beep sound"
        }
    ]

    # Stahování souborů
    success_count = 0
    for audio in audio_files:
        if download_file(audio["url"], audio["filename"]):
            success_count += 1
            print(f"   📝 {audio['description']}")

    print(f"\n🎯 Staženo {success_count}/{len(audio_files)} audio souborů")

    # Pokus o kopírování battle_march.wav, pokud existuje
    if os.path.exists("battle_march.wav"):
        try:
            shutil.copy("battle_march.wav", "assets/music/battle_march.wav")
            print("✅ Zkopírován battle_march.wav do assets/music/")
        except Exception as e:
            print(f"❌ Chyba při kopírování: {e}")

    # Vytvoření jednoduchých zvukových efektů jako fallback
    print("\n🔧 Vytváření fallback zvuků...")

    try:
        import pygame
        pygame.mixer.init()

        # Jednoduchý beep zvuk
        import numpy as np

        def create_beep(frequency, duration, sample_rate=22050):
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            wave = np.sin(frequency * t * 2 * np.pi)
            wave = (wave * 32767).astype(np.int16)
            return wave

        # Vytvoření základních zvuků
        sounds = [
            {"name": "shoot.wav", "freq": 800, "duration": 0.1},
            {"name": "hit.wav", "freq": 400, "duration": 0.2},
            {"name": "powerup.wav", "freq": 600, "duration": 0.3}
        ]

        for sound in sounds:
            wave = create_beep(sound["freq"], sound["duration"])

            # Uložení jako WAV
            import wave as wav_module
            filename = f"assets/sounds/{sound['name']}"
            with wav_module.open(filename, 'w') as wav_file:
                wav_file.setnchannels(1)
                wav_file.setsampwidth(2)
                wav_file.setframerate(22050)
                wav_file.writeframes(wave.tobytes())

            print(f"✅ Vytvořen fallback: {sound['name']}")

    except Exception as e:
        print(f"❌ Chyba při vytváření fallback zvuků: {e}")

    print("\n🎮 Audio assety připraveny!")

if __name__ == "__main__":
    main()
