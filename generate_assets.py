#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Spitfire Game Asset Generator
----------------------------
This script generates simple placeholder assets for the Spitfire game.
"""

import os
import pygame
import math
import random

# Initialize Pygame
pygame.init()

# Constants
WIDTH, HEIGHT = 800, 600
TRANSPARENT = (0, 0, 0, 0)
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
GRAY = (150, 150, 150)
BROWN = (139, 69, 19)

def create_directories():
    """Create necessary directories for assets."""
    dirs = [
        "assets",
        "assets/images",
        "assets/sounds",
        "assets/music"
    ]

    for directory in dirs:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")

def generate_spitfire():
    """Generate a detailed Spitfire aircraft sprite (top-down view)."""
    surface = pygame.Surface((50, 70), pygame.SRCALPHA)

    # Stín letadla
    shadow_surface = pygame.Surface((52, 72), pygame.SRCALPHA)
    pygame.draw.ellipse(shadow_surface, (0, 0, 0, 60), (1, 1, 50, 70))
    surface.blit(shadow_surface, (-1, -1))

    # Hlavní trup (fuselage) - eliptický
    pygame.draw.ellipse(surface, (70, 130, 180), (18, 5, 14, 55))

    # Křídla - realistický tvar
    wing_points = [(5, 25), (45, 25), (45, 35), (35, 40), (15, 40), (5, 35)]
    pygame.draw.polygon(surface, (70, 130, 180), wing_points)

    # Ocasní plochy
    tail_points = [(20, 55), (30, 55), (28, 65), (22, 65)]
    pygame.draw.polygon(surface, (70, 130, 180), tail_points)

    # Vertikální ocasní plocha
    pygame.draw.polygon(surface, (70, 130, 180), [(23, 60), (27, 60), (25, 68)])

    # Kokpit - průhledný
    pygame.draw.ellipse(surface, (200, 220, 255, 180), (20, 15, 10, 15))
    pygame.draw.ellipse(surface, (150, 170, 200), (21, 16, 8, 13), 1)

    # Vrtule - rotující efekt
    pygame.draw.ellipse(surface, (50, 50, 50), (22, 2, 6, 8))
    pygame.draw.line(surface, (30, 30, 30), (25, 0), (25, 10), 2)
    pygame.draw.line(surface, (30, 30, 30), (20, 6), (30, 6), 2)

    # RAF roundel na křídlech - detailnější
    # Levé křídlo
    pygame.draw.circle(surface, (200, 16, 46), (15, 32), 6)
    pygame.draw.circle(surface, WHITE, (15, 32), 4)
    pygame.draw.circle(surface, (0, 47, 167), (15, 32), 2)

    # Pravé křídlo
    pygame.draw.circle(surface, (200, 16, 46), (35, 32), 6)
    pygame.draw.circle(surface, WHITE, (35, 32), 4)
    pygame.draw.circle(surface, (0, 47, 167), (35, 32), 2)

    # Detaily motoru
    pygame.draw.ellipse(surface, (40, 40, 40), (20, 8, 10, 6))

    # Výfukové potrubí
    pygame.draw.rect(surface, (60, 60, 60), (16, 20, 2, 8))
    pygame.draw.rect(surface, (60, 60, 60), (32, 20, 2, 8))

    pygame.image.save(surface, "assets/images/spitfire.png")
    print("Generated enhanced spitfire.png")

def generate_enemy():
    """Generate a detailed enemy aircraft sprite (top-down view, facing down)."""
    surface = pygame.Surface((45, 65), pygame.SRCALPHA)

    # Stín letadla
    shadow_surface = pygame.Surface((47, 67), pygame.SRCALPHA)
    pygame.draw.ellipse(shadow_surface, (0, 0, 0, 60), (1, 1, 45, 65))
    surface.blit(shadow_surface, (-1, -1))

    # Hlavní trup (fuselage) - tmavší barva pro nepřítele
    pygame.draw.ellipse(surface, (60, 60, 60), (16, 10, 13, 50))

    # Křídla - úhlový design
    wing_points = [(3, 35), (42, 35), (42, 45), (32, 50), (13, 50), (3, 45)]
    pygame.draw.polygon(surface, (60, 60, 60), wing_points)

    # Ocasní plochy
    tail_points = [(18, 55), (27, 55), (25, 63), (20, 63)]
    pygame.draw.polygon(surface, (60, 60, 60), tail_points)

    # Vertikální ocasní plocha
    pygame.draw.polygon(surface, (60, 60, 60), [(20, 58), (25, 58), (22.5, 65)])

    # Kokpit - tmavší
    pygame.draw.ellipse(surface, (40, 40, 40), (18, 20, 9, 12))
    pygame.draw.ellipse(surface, (80, 80, 80), (19, 21, 7, 10), 1)

    # Motor a vrtule
    pygame.draw.ellipse(surface, (30, 30, 30), (19, 5, 7, 10))
    pygame.draw.line(surface, (20, 20, 20), (22.5, 2), (22.5, 15), 2)
    pygame.draw.line(surface, (20, 20, 20), (17, 10), (28, 10), 2)

    # Německý kříž na křídlech
    # Levé křídlo
    pygame.draw.rect(surface, WHITE, (10, 38, 8, 8))
    pygame.draw.line(surface, BLACK, (10, 42), (18, 42), 2)
    pygame.draw.line(surface, BLACK, (14, 38), (14, 46), 2)

    # Pravé křídlo
    pygame.draw.rect(surface, WHITE, (27, 38, 8, 8))
    pygame.draw.line(surface, BLACK, (27, 42), (35, 42), 2)
    pygame.draw.line(surface, BLACK, (31, 38), (31, 46), 2)

    # Výzbroj - kanóny na křídlech
    pygame.draw.rect(surface, (40, 40, 40), (8, 42, 3, 6))
    pygame.draw.rect(surface, (40, 40, 40), (34, 42, 3, 6))

    pygame.image.save(surface, "assets/images/enemy.png")
    print("Generated enhanced enemy.png")

def generate_boss():
    """Generate a simple boss aircraft sprite (top-down view, facing down)."""
    surface = pygame.Surface((120, 120), pygame.SRCALPHA)

    # Main body (fuselage) - note the reversed orientation
    pygame.draw.ellipse(surface, BLACK, (45, 10, 30, 100))

    # Wings
    pygame.draw.polygon(surface, BLACK, [(5, 70), (115, 70), (115, 40), (5, 40)])

    # Tail (now at the top since it's facing down)
    pygame.draw.polygon(surface, BLACK, [(45, 10), (75, 10), (60, 0)])

    # Engines on wings
    pygame.draw.ellipse(surface, GRAY, (20, 50, 15, 25))
    pygame.draw.ellipse(surface, GRAY, (85, 50, 15, 25))

    # Cockpit (moved to the bottom part)
    pygame.draw.ellipse(surface, GRAY, (50, 80, 20, 20))

    # German crosses on wings
    # Left wing
    pygame.draw.rect(surface, WHITE, (25, 50, 15, 15))
    pygame.draw.line(surface, BLACK, (25, 57), (40, 57), 3)
    pygame.draw.line(surface, BLACK, (32, 50), (32, 65), 3)

    # Right wing
    pygame.draw.rect(surface, WHITE, (80, 50, 15, 15))
    pygame.draw.line(surface, BLACK, (80, 57), (95, 57), 3)
    pygame.draw.line(surface, BLACK, (87, 50), (87, 65), 3)

    # Add gun muzzles to show it can shoot
    pygame.draw.rect(surface, (100, 100, 100), (50, 100, 5, 10))
    pygame.draw.rect(surface, (100, 100, 100), (65, 100, 5, 10))

    pygame.image.save(surface, "assets/images/boss.png")
    print("Generated boss.png")

def generate_bullet():
    """Generate a simple bullet sprite (top-down view)."""
    surface = pygame.Surface((8, 16), pygame.SRCALPHA)

    # Bullet body
    pygame.draw.ellipse(surface, YELLOW, (0, 0, 8, 16))

    # Bullet trail
    pygame.draw.ellipse(surface, (255, 200, 0, 128), (2, 12, 4, 8))

    pygame.image.save(surface, "assets/images/bullet.png")
    print("Generated bullet.png")

def generate_enemy_bullet():
    """Generate a simple enemy bullet sprite (top-down view, facing down)."""
    surface = pygame.Surface((8, 16), pygame.SRCALPHA)

    # Bullet body
    pygame.draw.ellipse(surface, RED, (0, 0, 8, 16))

    # Bullet trail
    pygame.draw.ellipse(surface, (255, 100, 0, 128), (2, 0, 4, 8))

    pygame.image.save(surface, "assets/images/enemy_bullet.png")
    print("Generated enemy_bullet.png")

def generate_explosion_frames():
    """Generate simple explosion animation frames."""
    for i in range(1, 10):
        size = 50 + i * 5
        surface = pygame.Surface((size, size), pygame.SRCALPHA)

        # Explosion circle
        radius = size // 2 - 5
        center = (size // 2, size // 2)

        # Outer circle
        pygame.draw.circle(surface, YELLOW, center, radius)

        # Inner circle
        inner_radius = max(0, radius - 10)
        pygame.draw.circle(surface, RED, center, inner_radius)

        # Add some random "debris" particles
        for _ in range(10):
            angle = math.radians(random.randint(0, 360))
            distance = random.randint(radius // 2, radius)
            x = center[0] + int(math.cos(angle) * distance)
            y = center[1] + int(math.sin(angle) * distance)

            particle_size = random.randint(2, 5)
            pygame.draw.circle(surface, WHITE, (x, y), particle_size)

        pygame.image.save(surface, f"assets/images/explosion{i}.png")

    print("Generated explosion animation frames")

def generate_airfield():
    """Generate a detailed airfield sprite (top-down view)."""
    surface = pygame.Surface((300, 150), pygame.SRCALPHA)

    # Travnaté pozadí
    pygame.draw.rect(surface, (80, 150, 80), (0, 0, 300, 150))

    # Přidání textury trávy
    for i in range(0, 300, 10):
        for j in range(0, 150, 10):
            if random.randint(0, 3) == 0:
                pygame.draw.circle(surface, (70, 140, 70), (i + random.randint(0, 8), j + random.randint(0, 8)), 1)

    # Hlavní ranvej - delší a realističtější
    runway_width = 40
    runway_x = (300 - runway_width) // 2
    pygame.draw.rect(surface, (60, 60, 60), (runway_x, 10, runway_width, 130))

    # Okraje ranveje
    pygame.draw.rect(surface, (80, 80, 80), (runway_x - 2, 10, 2, 130))
    pygame.draw.rect(surface, (80, 80, 80), (runway_x + runway_width, 10, 2, 130))

    # Středová čára ranveje
    for i in range(15, 135, 15):
        pygame.draw.rect(surface, WHITE, (runway_x + runway_width//2 - 2, i, 4, 8))

    # Čísla ranveje
    font_surface = pygame.Surface((20, 30), pygame.SRCALPHA)
    pygame.draw.rect(font_surface, WHITE, (2, 2, 16, 26))
    pygame.draw.rect(font_surface, (60, 60, 60), (4, 4, 12, 22))
    surface.blit(font_surface, (runway_x + 10, 20))
    surface.blit(font_surface, (runway_x + 10, 100))

    # Terminálová budova
    terminal_x, terminal_y = 50, 50
    pygame.draw.rect(surface, (180, 180, 180), (terminal_x, terminal_y, 60, 40))
    pygame.draw.rect(surface, (150, 150, 150), (terminal_x, terminal_y - 5, 60, 5))  # Střecha

    # Okna terminálu
    for i in range(3):
        pygame.draw.rect(surface, (100, 150, 200), (terminal_x + 10 + i * 15, terminal_y + 10, 8, 12))

    # Řídící věž
    tower_x, tower_y = 70, 30
    pygame.draw.rect(surface, (200, 200, 200), (tower_x, tower_y, 20, 20))
    pygame.draw.rect(surface, (100, 150, 200), (tower_x + 2, tower_y + 5, 16, 10))  # Okna

    # Anténa na věži
    pygame.draw.line(surface, BLACK, (tower_x + 10, tower_y), (tower_x + 10, tower_y - 8), 2)
    pygame.draw.circle(surface, RED, (tower_x + 10, tower_y - 8), 2)

    # Hangáry
    hangar1_x, hangar1_y = 200, 40
    pygame.draw.rect(surface, (120, 120, 120), (hangar1_x, hangar1_y, 40, 30))
    pygame.draw.polygon(surface, (100, 100, 100), [(hangar1_x, hangar1_y), (hangar1_x + 40, hangar1_y), (hangar1_x + 20, hangar1_y - 8)])

    hangar2_x, hangar2_y = 200, 80
    pygame.draw.rect(surface, (120, 120, 120), (hangar2_x, hangar2_y, 40, 30))
    pygame.draw.polygon(surface, (100, 100, 100), [(hangar2_x, hangar2_y), (hangar2_x + 40, hangar2_y), (hangar2_x + 20, hangar2_y - 8)])

    # Pojezdové dráhy
    pygame.draw.rect(surface, (100, 100, 100), (runway_x - 20, 70, 20, 8))
    pygame.draw.rect(surface, (100, 100, 100), (runway_x + runway_width, 70, 20, 8))

    # Parkovací plochy pro letadla
    for i in range(3):
        park_x = 250
        park_y = 45 + i * 20
        pygame.draw.rect(surface, (90, 90, 90), (park_x, park_y, 25, 15))
        pygame.draw.rect(surface, YELLOW, (park_x, park_y, 25, 15), 1)

    # Světla na ranveji
    for i in range(20, 130, 20):
        pygame.draw.circle(surface, WHITE, (runway_x - 5, i), 2)
        pygame.draw.circle(surface, WHITE, (runway_x + runway_width + 5, i), 2)

    pygame.image.save(surface, "assets/images/airfield.png")
    print("Generated enhanced airfield.png")

def generate_ally():
    """Generate a simple ally sprite (top-down view)."""
    surface = pygame.Surface((20, 20), pygame.SRCALPHA)

    # Body circle (top-down view of soldier)
    pygame.draw.circle(surface, GREEN, (10, 10), 8)

    # Helmet (darker circle in the middle)
    pygame.draw.circle(surface, (0, 100, 0), (10, 10), 5)

    # Face indication
    pygame.draw.circle(surface, (222, 184, 135), (10, 8), 3)

    # Gun indication
    pygame.draw.rect(surface, (100, 100, 100), (12, 5, 8, 2))

    pygame.image.save(surface, "assets/images/ally.png")
    print("Generated ally.png")

def generate_building():
    """Generate a simple building sprite (top-down view)."""
    surface = pygame.Surface((60, 60), pygame.SRCALPHA)

    # Building base (square from top-down view)
    pygame.draw.rect(surface, GRAY, (0, 0, 60, 60))

    # Roof details (to give 3D impression)
    pygame.draw.rect(surface, (100, 100, 100), (5, 5, 50, 50))
    pygame.draw.rect(surface, (120, 120, 120), (10, 10, 40, 40))

    # Roof center
    pygame.draw.rect(surface, (150, 150, 150), (20, 20, 20, 20))

    # Windows or details on the roof
    pygame.draw.rect(surface, BLUE, (25, 25, 10, 10))

    # Shadow
    shadow_surface = pygame.Surface((70, 70), pygame.SRCALPHA)
    pygame.draw.ellipse(shadow_surface, (0, 0, 0, 64), (5, 55, 60, 15))
    surface.blit(shadow_surface, (-5, -5))

    pygame.image.save(surface, "assets/images/building.png")
    print("Generated building.png")

def generate_tank():
    """Generate a simple tank sprite (top-down view)."""
    surface = pygame.Surface((40, 50), pygame.SRCALPHA)

    # Tank body (rectangle from top-down view)
    pygame.draw.rect(surface, GREEN, (5, 10, 30, 35))

    # Tank turret (circle from top-down view)
    pygame.draw.circle(surface, (0, 100, 0), (20, 25), 10)

    # Tank gun (line extending from turret)
    pygame.draw.rect(surface, BLACK, (20, 0, 2, 15))

    # Tank tracks
    pygame.draw.rect(surface, BLACK, (0, 10, 5, 35))
    pygame.draw.rect(surface, BLACK, (35, 10, 5, 35))

    # Track details
    for i in range(7):
        pygame.draw.line(surface, (50, 50, 50), (0, i * 5 + 10), (5, i * 5 + 10), 1)
        pygame.draw.line(surface, (50, 50, 50), (35, i * 5 + 10), (40, i * 5 + 10), 1)

    # Shadow
    shadow_surface = pygame.Surface((50, 60), pygame.SRCALPHA)
    pygame.draw.ellipse(shadow_surface, (0, 0, 0, 64), (5, 45, 30, 10))
    surface.blit(shadow_surface, (-5, -5))

    pygame.image.save(surface, "assets/images/tank.png")
    print("Generated tank.png")

def generate_health_powerup():
    """Generate a simple health powerup sprite."""
    surface = pygame.Surface((30, 30), pygame.SRCALPHA)

    # Background circle
    pygame.draw.circle(surface, RED, (15, 15), 15)

    # White cross
    pygame.draw.rect(surface, WHITE, (10, 5, 10, 20))
    pygame.draw.rect(surface, WHITE, (5, 10, 20, 10))

    pygame.image.save(surface, "assets/images/health_powerup.png")
    print("Generated health_powerup.png")

def generate_weapon_powerup():
    """Generate a simple weapon powerup sprite."""
    surface = pygame.Surface((30, 30), pygame.SRCALPHA)

    # Background circle
    pygame.draw.circle(surface, YELLOW, (15, 15), 15)

    # Bullet symbol
    pygame.draw.polygon(surface, BLACK, [(15, 5), (25, 25), (15, 20), (5, 25)])

    pygame.image.save(surface, "assets/images/weapon_powerup.png")
    print("Generated weapon_powerup.png")

def generate_background():
    """Generate a simple sky background with ground view (top-down perspective)."""
    surface = pygame.Surface((800, 1200))

    # Sky color (solid blue)
    surface.fill((135, 206, 235))

    # Generate ground pattern (fields, roads, etc. from top-down view)
    # First, create a grid of fields
    field_colors = [
        (120, 180, 70),  # Light green
        (100, 160, 50),  # Medium green
        (80, 140, 40),   # Dark green
        (180, 160, 100), # Wheat field
        (160, 140, 80),  # Plowed field
    ]

    # Create a grid of fields
    field_size = 100
    for x in range(0, 800, field_size):
        for y in range(0, 1200, field_size):
            field_color = random.choice(field_colors)
            # Add some variation to the color
            variation = random.randint(-20, 20)
            field_color = (
                max(0, min(255, field_color[0] + variation)),
                max(0, min(255, field_color[1] + variation)),
                max(0, min(255, field_color[2] + variation))
            )
            pygame.draw.rect(surface, field_color, (x, y, field_size, field_size))

    # Add some roads
    road_color = (80, 80, 80)
    # Horizontal roads
    for y in range(200, 1200, 300):
        pygame.draw.rect(surface, road_color, (0, y, 800, 15))

    # Vertical roads
    for x in range(200, 800, 300):
        pygame.draw.rect(surface, road_color, (x, 0, 15, 1200))

    # Add some rivers
    river_color = (100, 150, 200)
    river_points = []
    start_x = random.randint(100, 700)
    for y in range(0, 1200, 50):
        # Make the river meander
        start_x += random.randint(-30, 30)
        start_x = max(50, min(750, start_x))
        river_points.append((start_x, y))

    # Draw the river
    if len(river_points) > 1:
        pygame.draw.lines(surface, river_color, False, river_points, 20)

    # Add some small towns/villages
    for _ in range(5):
        town_x = random.randint(50, 750)
        town_y = random.randint(50, 1150)
        town_size = random.randint(30, 60)

        # Town center
        pygame.draw.rect(surface, (150, 150, 150), (town_x, town_y, town_size, town_size))

        # Buildings around
        for i in range(8):
            building_x = town_x + random.randint(-town_size, town_size)
            building_y = town_y + random.randint(-town_size, town_size)
            building_size = random.randint(10, 20)
            pygame.draw.rect(surface, (180, 180, 180), (building_x, building_y, building_size, building_size))

    # Add some clouds (shadows on the ground)
    for _ in range(15):
        cloud_x = random.randint(0, 800)
        cloud_y = random.randint(0, 1200)
        cloud_size = random.randint(50, 150)

        # Cloud shadow
        shadow_surface = pygame.Surface((cloud_size, cloud_size), pygame.SRCALPHA)
        pygame.draw.ellipse(shadow_surface, (0, 0, 0, 30), (0, 0, cloud_size, cloud_size // 2))
        surface.blit(shadow_surface, (cloud_x, cloud_y))

    pygame.image.save(surface, "assets/images/background.png")
    print("Generated background.png")

    # Level 1 background (countryside)
    surface.fill((135, 206, 235))  # Sky blue

    # Create a grid of fields (more green for countryside)
    field_colors = [
        (120, 180, 70),  # Light green
        (100, 160, 50),  # Medium green
        (80, 140, 40),   # Dark green
        (140, 190, 80),  # Lighter green
    ]

    field_size = 120
    for x in range(0, 800, field_size):
        for y in range(0, 1200, field_size):
            field_color = random.choice(field_colors)
            # Add some variation to the color
            variation = random.randint(-20, 20)
            field_color = (
                max(0, min(255, field_color[0] + variation)),
                max(0, min(255, field_color[1] + variation)),
                max(0, min(255, field_color[2] + variation))
            )
            pygame.draw.rect(surface, field_color, (x, y, field_size, field_size))

    # Add some country roads (fewer and smaller)
    road_color = (150, 140, 130)  # Dirt road color
    # Horizontal roads
    for y in range(300, 1200, 400):
        pygame.draw.rect(surface, road_color, (0, y, 800, 10))

    # Vertical roads
    for x in range(300, 800, 400):
        pygame.draw.rect(surface, road_color, (x, 0, 10, 1200))

    # Add a river
    river_color = (100, 150, 200)
    river_points = []
    start_x = random.randint(100, 700)
    for y in range(0, 1200, 50):
        # Make the river meander
        start_x += random.randint(-30, 30)
        start_x = max(50, min(750, start_x))
        river_points.append((start_x, y))

    # Draw the river
    if len(river_points) > 1:
        pygame.draw.lines(surface, river_color, False, river_points, 25)

    # Add some small farms
    for _ in range(8):
        farm_x = random.randint(50, 750)
        farm_y = random.randint(50, 1150)

        # Farmhouse
        pygame.draw.rect(surface, (180, 150, 120), (farm_x, farm_y, 20, 20))

        # Barn
        pygame.draw.rect(surface, (150, 80, 50), (farm_x + 30, farm_y, 25, 15))

    # Add some cloud shadows
    for _ in range(10):
        cloud_x = random.randint(0, 800)
        cloud_y = random.randint(0, 1200)
        cloud_size = random.randint(50, 150)

        # Cloud shadow
        shadow_surface = pygame.Surface((cloud_size, cloud_size), pygame.SRCALPHA)
        pygame.draw.ellipse(shadow_surface, (0, 0, 0, 20), (0, 0, cloud_size, cloud_size // 2))
        surface.blit(shadow_surface, (cloud_x, cloud_y))

    pygame.image.save(surface, "assets/images/background_level1.png")
    print("Generated background_level1.png")

    # Level 2 background (industrial/urban)
    surface.fill((135, 206, 235))  # Sky blue

    # Create a grid of urban areas
    urban_colors = [
        (150, 150, 150),  # Concrete
        (130, 130, 130),  # Asphalt
        (160, 160, 160),  # Buildings
        (100, 100, 100),  # Industrial
    ]

    block_size = 80
    for x in range(0, 800, block_size):
        for y in range(0, 1200, block_size):
            block_color = random.choice(urban_colors)
            # Add some variation to the color
            variation = random.randint(-10, 10)
            block_color = (
                max(0, min(255, block_color[0] + variation)),
                max(0, min(255, block_color[1] + variation)),
                max(0, min(255, block_color[2] + variation))
            )
            pygame.draw.rect(surface, block_color, (x, y, block_size, block_size))

    # Add a grid of streets
    street_color = (50, 50, 50)
    for x in range(0, 800, block_size):
        pygame.draw.rect(surface, street_color, (x-5, 0, 10, 1200))

    for y in range(0, 1200, block_size):
        pygame.draw.rect(surface, street_color, (0, y-5, 800, 10))

    # Add some larger buildings
    for _ in range(20):
        building_x = random.randint(50, 750)
        building_y = random.randint(50, 1150)
        building_size = random.randint(20, 40)
        building_color = (
            random.randint(100, 180),
            random.randint(100, 180),
            random.randint(100, 180)
        )
        pygame.draw.rect(surface, building_color, (building_x, building_y, building_size, building_size))

    # Add some factories
    for _ in range(5):
        factory_x = random.randint(100, 700)
        factory_y = random.randint(100, 1100)

        # Main building
        pygame.draw.rect(surface, (120, 100, 80), (factory_x, factory_y, 60, 40))

        # Smokestacks
        pygame.draw.rect(surface, (80, 80, 80), (factory_x + 10, factory_y - 10, 8, 10))
        pygame.draw.rect(surface, (80, 80, 80), (factory_x + 30, factory_y - 15, 10, 15))

        # Smoke
        smoke_surface = pygame.Surface((30, 20), pygame.SRCALPHA)
        pygame.draw.ellipse(smoke_surface, (200, 200, 200, 100), (0, 0, 30, 20))
        surface.blit(smoke_surface, (factory_x + 5, factory_y - 25))

        smoke_surface = pygame.Surface((40, 25), pygame.SRCALPHA)
        pygame.draw.ellipse(smoke_surface, (200, 200, 200, 80), (0, 0, 40, 25))
        surface.blit(smoke_surface, (factory_x + 25, factory_y - 35))

    # Add some cloud shadows
    for _ in range(8):
        cloud_x = random.randint(0, 800)
        cloud_y = random.randint(0, 1200)
        cloud_size = random.randint(50, 150)

        # Cloud shadow
        shadow_surface = pygame.Surface((cloud_size, cloud_size), pygame.SRCALPHA)
        pygame.draw.ellipse(shadow_surface, (0, 0, 0, 40), (0, 0, cloud_size, cloud_size // 2))
        surface.blit(shadow_surface, (cloud_x, cloud_y))

    pygame.image.save(surface, "assets/images/background_level2.png")
    print("Generated background_level2.png")

def main():
    """Generate all game assets."""

    # Create necessary directories
    create_directories()

    # Generate sprites
    generate_spitfire()
    generate_enemy()
    generate_boss()
    generate_bullet()
    generate_enemy_bullet()  # Add enemy bullet
    generate_explosion_frames()
    generate_airfield()
    generate_ally()
    generate_building()
    generate_tank()
    generate_health_powerup()
    generate_weapon_powerup()
    generate_background()

    print("Asset generation complete!")

if __name__ == "__main__":
    main()