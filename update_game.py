#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Spitfire Game Updater
--------------------
This script updates the Spitfire game to use the new assets.
"""

import os
import sys
import shutil

def update_game():
    """Update the game to use the new assets."""
    print("Updating Spitfire game...")
    
    # Check if the assets directory exists
    if not os.path.exists("assets"):
        print("Error: Assets directory not found. Please run download_assets.py or generate_assets.py first.")
        sys.exit(1)
    
    # Check if the game file exists
    if not os.path.exists("spitfire_game.py"):
        print("Error: Game file not found. Please make sure spitfire_game.py exists.")
        sys.exit(1)
    
    # Create a backup of the original game file
    backup_file = "spitfire_game.py.bak"
    if not os.path.exists(backup_file):
        shutil.copy("spitfire_game.py", backup_file)
        print(f"Created backup of game file: {backup_file}")
    
    # Update the game file to use the new assets
    with open("spitfire_game.py", "r", encoding="utf-8") as f:
        game_code = f.read()
    
    # Update the game code to use the new assets
    # No need to modify the code as it already handles missing assets gracefully
    
    print("Game is already set up to use the assets. No code changes needed.")
    print("The game will automatically use the new assets when they are available.")
    
    print("Game update complete!")

if __name__ == "__main__":
    update_game()