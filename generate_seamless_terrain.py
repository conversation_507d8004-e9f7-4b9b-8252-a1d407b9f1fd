#!/usr/bin/env python3
"""
Generátor seamless terrain tilesetů pro hru Spitfire
Vytváří textury, které se plynule navazují na sebe
"""

import pygame
import random
import math
import os

# Inicializace pygame
pygame.init()

# Konstanty
TILE_SIZE = 32
TILESET_WIDTH = 16  # počet dlaždic na šířku
TILESET_HEIGHT = 16  # počet dlaždic na výšku

# Barvy pro různé typy terénu
TERRAIN_COLORS = {
    'grass': (85, 140, 60),
    'dirt': (139, 115, 85),
    'water': (80, 130, 180),
    'sand': (194, 178, 128),
    'stone': (120, 120, 120),
    'forest': (60, 100, 40),
    'road': (90, 85, 80),
    'farmland': (160, 140, 80),
    'village': (120, 100, 80)
}

def generate_noise_texture(width, height, base_color, variation=20):
    """Generuje texturu s šumem pro přirozenější vzhled"""
    surface = pygame.Surface((width, height))

    for x in range(width):
        for y in range(height):
            # Perlin-like noise
            noise_value = (
                math.sin(x * 0.1) * math.cos(y * 0.1) * 0.3 +
                math.sin(x * 0.05) * math.cos(y * 0.05) * 0.5 +
                random.uniform(-0.2, 0.2)
            )

            # Aplikace šumu na barvu
            r = max(0, min(255, base_color[0] + int(noise_value * variation)))
            g = max(0, min(255, base_color[1] + int(noise_value * variation)))
            b = max(0, min(255, base_color[2] + int(noise_value * variation)))

            surface.set_at((x, y), (r, g, b))

    return surface

def create_transition_tile(terrain1, terrain2, direction):
    """
    Vytváří přechodovou dlaždici mezi dvěma typy terénu
    direction: 'top', 'bottom', 'left', 'right', 'topleft', 'topright', 'bottomleft', 'bottomright'
    """
    surface = pygame.Surface((TILE_SIZE, TILE_SIZE))

    color1 = TERRAIN_COLORS[terrain1]
    color2 = TERRAIN_COLORS[terrain2]

    for x in range(TILE_SIZE):
        for y in range(TILE_SIZE):
            # Výpočet blending faktoru podle směru
            if direction == 'top':
                blend = y / TILE_SIZE
            elif direction == 'bottom':
                blend = 1 - (y / TILE_SIZE)
            elif direction == 'left':
                blend = x / TILE_SIZE
            elif direction == 'right':
                blend = 1 - (x / TILE_SIZE)
            elif direction == 'topleft':
                blend = (x + y) / (2 * TILE_SIZE)
            elif direction == 'topright':
                blend = (TILE_SIZE - x + y) / (2 * TILE_SIZE)
            elif direction == 'bottomleft':
                blend = (x + TILE_SIZE - y) / (2 * TILE_SIZE)
            elif direction == 'bottomright':
                blend = (2 * TILE_SIZE - x - y) / (2 * TILE_SIZE)
            else:
                blend = 0.5

            # Smooth transition s noise
            noise = random.uniform(-0.1, 0.1)
            blend = max(0, min(1, blend + noise))

            # Interpolace barev
            r = int(color1[0] * (1 - blend) + color2[0] * blend)
            g = int(color1[1] * (1 - blend) + color2[1] * blend)
            b = int(color1[2] * (1 - blend) + color2[2] * blend)

            surface.set_at((x, y), (r, g, b))

    return surface

def create_grass_base(surface, size):
    """Vytváří pokročilý travnatý povrch s realistickými efekty"""
    base_colors = [
        (85, 140, 60),   # Základní zelená
        (95, 150, 70),   # Světlejší zelená
        (75, 130, 50),   # Tmavší zelená
        (90, 145, 65)    # Střední zelená
    ]

    # Vytvoření komplexního noise patternu
    for y in range(size):
        for x in range(size):
            # Vícevrstvý Perlin noise
            noise1 = math.sin(x * 0.02) * math.cos(y * 0.02)
            noise2 = math.sin(x * 0.05) * math.cos(y * 0.05) * 0.5
            noise3 = math.sin(x * 0.1) * math.cos(y * 0.1) * 0.25
            noise4 = random.uniform(-0.2, 0.2)

            combined_noise = noise1 + noise2 + noise3 + noise4

            # Výběr základní barvy podle pozice
            color_index = int(abs(combined_noise * 2)) % len(base_colors)
            base_color = base_colors[color_index]

            # Aplikace variace
            variation = int(combined_noise * 30)
            color = (
                max(30, min(255, base_color[0] + variation)),
                max(30, min(255, base_color[1] + variation)),
                max(30, min(255, base_color[2] + variation))
            )
            surface.set_at((x, y), color)

    # Přidání travních stébel
    add_grass_blades(surface, size)

def add_grass_blades(surface, size):
    """Přidává jednotlivá travní stébla pro realismus"""
    blade_colors = [(60, 120, 40), (80, 140, 60), (70, 130, 50)]

    for _ in range(size // 2):  # Hustota stébel
        x = random.randint(0, size - 1)
        y = random.randint(0, size - 1)

        # Náhodná délka a směr stébla
        length = random.randint(2, 6)
        angle = random.uniform(-0.3, 0.3)  # Mírný sklon

        color = random.choice(blade_colors)

        # Vykreslení stébla jako tenké čáry
        end_x = x + int(length * math.sin(angle))
        end_y = y - length

        if 0 <= end_x < size and 0 <= end_y < size:
            pygame.draw.line(surface, color, (x, y), (end_x, end_y), 1)

def create_dirt_path(surface, size):
    """Vytváří zemní stezku přes texturu"""
    path_color = (120, 100, 70)
    path_width = random.randint(8, 16)

    # Meandrující stezka
    points = []
    start_x = random.randint(0, size//4)
    start_y = 0

    for y in range(0, size, 10):
        x_offset = int(30 * math.sin(y * 0.02)) + random.randint(-5, 5)
        x = start_x + x_offset
        x = max(path_width, min(size - path_width, x))
        points.append((x, y))

    # Vykreslení stezky
    for i in range(len(points) - 1):
        pygame.draw.line(surface, path_color, points[i], points[i+1], path_width)

        # Přidání okrajů stezky
        edge_color = (100, 80, 50)
        pygame.draw.line(surface, edge_color, points[i], points[i+1], path_width + 4)
        pygame.draw.line(surface, path_color, points[i], points[i+1], path_width)

def draw_detailed_tree(surface, x, y, size):
    """Nakreslí detailní strom s větvemi"""
    # Kmen
    trunk_color = (101, 67, 33)
    trunk_width = max(2, size // 4)
    trunk_height = size // 2

    # Kmen s texturou
    for i in range(trunk_height):
        trunk_y = y + i
        color_var = random.randint(-10, 10)
        trunk_shade = (
            max(0, min(255, trunk_color[0] + color_var)),
            max(0, min(255, trunk_color[1] + color_var)),
            max(0, min(255, trunk_color[2] + color_var))
        )
        pygame.draw.line(surface, trunk_shade,
                        (x - trunk_width//2, trunk_y),
                        (x + trunk_width//2, trunk_y))

    # Koruna stromu - více vrstev
    crown_colors = [(34, 139, 34), (50, 160, 50), (20, 120, 20)]
    crown_sizes = [size, size * 0.8, size * 0.6]

    for i, (crown_color, crown_size) in enumerate(zip(crown_colors, crown_sizes)):
        offset_x = random.randint(-2, 2)
        offset_y = random.randint(-2, 2)
        pygame.draw.circle(surface, crown_color,
                          (x + offset_x, y + offset_y), int(crown_size))

    # Přidání větví
    for _ in range(random.randint(2, 5)):
        branch_start_x = x + random.randint(-trunk_width, trunk_width)
        branch_start_y = y + random.randint(0, trunk_height//2)
        branch_end_x = branch_start_x + random.randint(-size//2, size//2)
        branch_end_y = branch_start_y - random.randint(5, 15)

        pygame.draw.line(surface, trunk_color,
                        (branch_start_x, branch_start_y),
                        (branch_end_x, branch_end_y), 2)

def draw_bush(surface, x, y, size):
    """Nakreslí keř"""
    bush_colors = [(60, 120, 40), (80, 140, 60), (40, 100, 30)]

    for i, color in enumerate(bush_colors):
        offset_x = random.randint(-2, 2)
        offset_y = random.randint(-2, 2)
        bush_size = size - i * 2
        pygame.draw.circle(surface, color, (x + offset_x, y + offset_y), bush_size)

def add_grass_details(surface, size):
    """Přidává malé detaily do trávy"""
    # Květiny
    flower_colors = [(255, 255, 0), (255, 100, 100), (100, 100, 255), (255, 255, 255)]
    for _ in range(random.randint(5, 15)):
        x = random.randint(0, size)
        y = random.randint(0, size)
        color = random.choice(flower_colors)
        pygame.draw.circle(surface, color, (x, y), 2)

    # Tmavší skvrny (stíny, mokrá místa)
    for _ in range(random.randint(3, 8)):
        x = random.randint(0, size)
        y = random.randint(0, size)
        patch_size = random.randint(5, 15)
        dark_color = (60, 100, 40)
        pygame.draw.circle(surface, dark_color, (x, y), patch_size)

def create_forest_base(surface, size):
    """Vytváří základní lesní podrost"""
    base_color = (40, 80, 30)

    # Tmavší základní barva pro les
    for y in range(size):
        for x in range(size):
            noise_value = (math.sin(x * 0.03) * math.cos(y * 0.03) +
                          random.uniform(-0.4, 0.4))
            variation = int(noise_value * 20)
            color = (
                max(20, min(255, base_color[0] + variation)),
                max(20, min(255, base_color[1] + variation)),
                max(20, min(255, base_color[2] + variation))
            )
            surface.set_at((x, y), color)

def create_forest_path(surface, size):
    """Vytváří lesní stezku"""
    path_color = (80, 60, 40)
    path_width = random.randint(6, 12)

    # Úzká lesní stezka
    points = []
    start_x = random.randint(size//4, 3*size//4)

    for y in range(0, size, 8):
        x_offset = int(20 * math.sin(y * 0.03)) + random.randint(-3, 3)
        x = start_x + x_offset
        x = max(path_width, min(size - path_width, x))
        points.append((x, y))

    for i in range(len(points) - 1):
        pygame.draw.line(surface, path_color, points[i], points[i+1], path_width)

def add_forest_details(surface, size):
    """Přidává lesní detaily"""
    # Padlé kmeny
    for _ in range(random.randint(1, 3)):
        log_x = random.randint(20, size - 20)
        log_y = random.randint(20, size - 20)
        log_length = random.randint(30, 60)
        log_color = (101, 67, 33)

        # Horizontální kmen
        pygame.draw.rect(surface, log_color, (log_x, log_y, log_length, 6))

    # Houby a malé rostliny
    for _ in range(random.randint(5, 12)):
        x = random.randint(0, size)
        y = random.randint(0, size)
        mushroom_color = random.choice([(139, 69, 19), (160, 82, 45), (205, 133, 63)])
        pygame.draw.circle(surface, mushroom_color, (x, y), 3)
        # Klobouček houby
        pygame.draw.circle(surface, (200, 200, 200), (x, y-2), 2)

def create_farmland_base(surface, size):
    """Vytváří základní půdu pro pole"""
    soil_color = (139, 115, 85)

    # Půda s brázdami
    for y in range(size):
        for x in range(size):
            # Vytvoření brázd
            furrow_pattern = math.sin(y * 0.3) * 10
            noise_value = random.uniform(-0.2, 0.2)
            variation = int((furrow_pattern + noise_value) * 15)

            color = (
                max(80, min(255, soil_color[0] + variation)),
                max(60, min(255, soil_color[1] + variation)),
                max(40, min(255, soil_color[2] + variation))
            )
            surface.set_at((x, y), color)

def create_crop_rows(surface, size, crop_type):
    """Vytváří řádky plodin"""
    if crop_type == 'wheat':
        crop_color = (218, 165, 32)  # Zlatá pšenice
        row_spacing = 10
    elif crop_type == 'corn':
        crop_color = (107, 142, 35)  # Zelená kukuřice
        row_spacing = 15
    else:  # vegetables
        crop_color = (50, 205, 50)  # Zelená zelenina
        row_spacing = 8

    # Vykreslení řádků
    for y in range(0, size, row_spacing):
        # Řádek plodin
        for x in range(0, size, 3):
            plant_x = x + random.randint(-1, 1)
            plant_y = y + random.randint(-2, 2)

            if 0 <= plant_x < size and 0 <= plant_y < size:
                if crop_type == 'wheat':
                    draw_wheat_plant(surface, plant_x, plant_y)
                elif crop_type == 'corn':
                    draw_corn_plant(surface, plant_x, plant_y)
                else:
                    draw_vegetable_plant(surface, plant_x, plant_y)

def draw_wheat_plant(surface, x, y):
    """Nakreslí rostlinu pšenice"""
    # Stéblo
    pygame.draw.line(surface, (218, 165, 32), (x, y), (x, y-6), 1)
    # Klas
    pygame.draw.circle(surface, (255, 215, 0), (x, y-6), 2)

def draw_corn_plant(surface, x, y):
    """Nakreslí rostlinu kukuřice"""
    # Vyšší stéblo
    pygame.draw.line(surface, (107, 142, 35), (x, y), (x, y-8), 2)
    # Listy
    pygame.draw.line(surface, (85, 107, 47), (x-2, y-4), (x+2, y-6), 1)
    pygame.draw.line(surface, (85, 107, 47), (x+2, y-4), (x-2, y-6), 1)

def draw_vegetable_plant(surface, x, y):
    """Nakreslí zeleninovou rostlinu"""
    # Malé listy
    pygame.draw.circle(surface, (50, 205, 50), (x, y), 2)
    pygame.draw.circle(surface, (34, 139, 34), (x-1, y-1), 1)

def add_farm_details(surface, size):
    """Přidává zemědělské detaily"""
    # Traktorové stopy
    if random.random() < 0.3:
        track_color = (100, 80, 60)
        track_y = random.randint(size//4, 3*size//4)
        pygame.draw.rect(surface, track_color, (0, track_y, size, 4))
        pygame.draw.rect(surface, track_color, (0, track_y + 8, size, 4))

    # Kameny v poli
    for _ in range(random.randint(2, 6)):
        x = random.randint(0, size)
        y = random.randint(0, size)
        stone_color = (169, 169, 169)
        pygame.draw.circle(surface, stone_color, (x, y), random.randint(2, 4))

def create_water_base(surface, size):
    """Vytváří pokročilou vodní texturu"""
    # Vícevrstvé barvy vody
    water_colors = [
        (60, 120, 180),   # Hluboká modrá
        (80, 140, 200),   # Střední modrá
        (100, 160, 220),  # Světlá modrá
        (70, 130, 190)    # Tmavší modrá
    ]

    for y in range(size):
        for x in range(size):
            # Simulace hloubky vody
            depth_noise = (math.sin(x * 0.03) * math.cos(y * 0.03) +
                          math.sin(x * 0.08) * math.cos(y * 0.08) * 0.3)

            # Výběr barvy podle "hloubky"
            depth_index = int(abs(depth_noise * 2)) % len(water_colors)
            base_color = water_colors[depth_index]

            # Přidání jemných variací
            variation = int(depth_noise * 20)
            color = (
                max(40, min(255, base_color[0] + variation)),
                max(80, min(255, base_color[1] + variation)),
                max(120, min(255, base_color[2] + variation))
            )
            surface.set_at((x, y), color)

def add_water_effects(surface, size):
    """Přidává vodní efekty - vlnky, odlesky"""
    # Animované vlnky
    for wave_set in range(3):
        wave_frequency = 0.02 + wave_set * 0.01
        wave_amplitude = 15 - wave_set * 3

        for y in range(0, size, 4):
            for x in range(size):
                wave_offset = int(wave_amplitude * math.sin(x * wave_frequency + wave_set))
                wave_y = y + wave_offset

                if 0 <= wave_y < size:
                    # Světlejší vlnka
                    current_color = surface.get_at((x, wave_y))
                    bright_color = (
                        min(255, current_color[0] + 20),
                        min(255, current_color[1] + 20),
                        min(255, current_color[2] + 30)
                    )
                    surface.set_at((x, wave_y), bright_color)

    # Odlesky světla
    for _ in range(random.randint(8, 15)):
        x = random.randint(0, size - 1)
        y = random.randint(0, size - 1)

        # Různé velikosti odlesků
        reflection_size = random.randint(3, 8)
        reflection_color = (200, 220, 255)

        # Kruhový odlesk
        for dx in range(-reflection_size, reflection_size + 1):
            for dy in range(-reflection_size, reflection_size + 1):
                if dx*dx + dy*dy <= reflection_size*reflection_size:
                    px, py = x + dx, y + dy
                    if 0 <= px < size and 0 <= py < size:
                        # Blend s existující barvou
                        current = surface.get_at((px, py))
                        alpha = 0.3
                        blended = (
                            int(current[0] * (1-alpha) + reflection_color[0] * alpha),
                            int(current[1] * (1-alpha) + reflection_color[1] * alpha),
                            int(current[2] * (1-alpha) + reflection_color[2] * alpha)
                        )
                        surface.set_at((px, py), blended)

def add_shoreline_details(surface, size):
    """Přidává detaily břehu"""
    # Kameny u břehu
    for _ in range(random.randint(3, 8)):
        x = random.randint(0, size - 1)
        y = random.randint(0, size - 1)

        stone_size = random.randint(4, 10)
        stone_color = random.choice([(120, 120, 120), (100, 100, 100), (140, 140, 140)])

        pygame.draw.circle(surface, stone_color, (x, y), stone_size)
        # Stín kamene
        shadow_color = (stone_color[0] - 30, stone_color[1] - 30, stone_color[2] - 30)
        pygame.draw.circle(surface, shadow_color, (x + 2, y + 2), stone_size - 1)

    # Vodní rostliny
    for _ in range(random.randint(2, 6)):
        x = random.randint(0, size - 1)
        y = random.randint(0, size - 1)

        # Lekníny
        lily_color = (34, 139, 34)
        pygame.draw.circle(surface, lily_color, (x, y), 6)
        # Květ leknínu
        flower_color = (255, 255, 255)
        pygame.draw.circle(surface, flower_color, (x, y), 2)

def create_village_base(surface, size):
    """Vytváří základní vesnicový povrch"""
    # Směs hlíny a trávy
    for y in range(size):
        for x in range(size):
            # Organická směs barev
            base_noise = math.sin(x * 0.04) * math.cos(y * 0.04) + random.uniform(-0.3, 0.3)

            if base_noise > 0.2:
                # Travnaté oblasti
                base_color = (85, 140, 60)
            else:
                # Hlínaté oblasti
                base_color = (120, 100, 80)

            variation = int(base_noise * 20)
            color = (
                max(40, min(255, base_color[0] + variation)),
                max(40, min(255, base_color[1] + variation)),
                max(40, min(255, base_color[2] + variation))
            )
            surface.set_at((x, y), color)

def create_village_roads(surface, size):
    """Vytváří organické vesnicové cesty"""
    road_color = (90, 85, 80)

    # Hlavní cesta - ne přesně uprostřed
    main_road_y = size//2 + random.randint(-10, 10)
    road_width = 12

    # Meandrující hlavní cesta
    for x in range(size):
        road_offset = int(5 * math.sin(x * 0.05))
        road_y = main_road_y + road_offset

        for dy in range(-road_width//2, road_width//2):
            py = road_y + dy
            if 0 <= py < size:
                surface.set_at((x, py), road_color)

    # Vedlejší cesty k budovám
    for _ in range(random.randint(2, 4)):
        side_road_x = random.randint(size//4, 3*size//4)
        side_road_width = 6

        for y in range(size):
            for dx in range(-side_road_width//2, side_road_width//2):
                px = side_road_x + dx + random.randint(-1, 1)
                if 0 <= px < size:
                    surface.set_at((px, y), road_color)

def create_village_buildings(surface, size):
    """Vytváří realistické vesnicové budovy"""
    buildings_placed = []

    for _ in range(random.randint(4, 8)):
        # Pokus o umístění budovy
        for attempt in range(20):  # Max 20 pokusů
            building_width = random.randint(20, 35)
            building_height = random.randint(15, 25)
            building_x = random.randint(5, size - building_width - 5)
            building_y = random.randint(5, size - building_height - 5)

            # Kontrola překrývání s existujícími budovami
            overlap = False
            for bx, by, bw, bh in buildings_placed:
                if (building_x < bx + bw + 5 and building_x + building_width + 5 > bx and
                    building_y < by + bh + 5 and building_y + building_height + 5 > by):
                    overlap = True
                    break

            if not overlap:
                draw_detailed_building(surface, building_x, building_y, building_width, building_height)
                buildings_placed.append((building_x, building_y, building_width, building_height))
                break

def draw_detailed_building(surface, x, y, width, height):
    """Nakreslí detailní budovu"""
    # Základní budova s texturou
    building_colors = [(180, 160, 140), (160, 140, 120), (200, 180, 160)]
    building_color = random.choice(building_colors)

    # Stěny s texturou
    for by in range(height):
        for bx in range(width):
            color_var = random.randint(-10, 10)
            wall_color = (
                max(100, min(255, building_color[0] + color_var)),
                max(80, min(255, building_color[1] + color_var)),
                max(60, min(255, building_color[2] + color_var))
            )
            surface.set_at((x + bx, y + by), wall_color)

    # Střecha
    roof_colors = [(139, 69, 19), (160, 82, 45), (101, 67, 33)]
    roof_color = random.choice(roof_colors)

    # Trojúhelníková střecha
    roof_height = 8
    for i in range(roof_height):
        roof_width = width - i * 2
        roof_start = x + i
        roof_y = y - roof_height + i

        if roof_y >= 0 and roof_width > 0:
            for rx in range(roof_width):
                if roof_start + rx < surface.get_width():
                    surface.set_at((roof_start + rx, roof_y), roof_color)

    # Okna
    window_color = (100, 150, 200)
    window_size = min(width//5, height//5, 6)

    # 2-4 okna
    num_windows = random.randint(2, 4)
    for i in range(num_windows):
        window_x = x + (i + 1) * width // (num_windows + 1) - window_size//2
        window_y = y + height//3

        # Okno s rámem
        frame_color = (80, 60, 40)
        pygame.draw.rect(surface, frame_color, (window_x-1, window_y-1, window_size+2, window_size+2))
        pygame.draw.rect(surface, window_color, (window_x, window_y, window_size, window_size))

        # Křížek v okně
        pygame.draw.line(surface, frame_color, (window_x, window_y + window_size//2),
                        (window_x + window_size, window_y + window_size//2), 1)
        pygame.draw.line(surface, frame_color, (window_x + window_size//2, window_y),
                        (window_x + window_size//2, window_y + window_size), 1)

    # Dveře
    door_color = (101, 67, 33)
    door_width = width // 4
    door_height = height // 2
    door_x = x + width//2 - door_width//2
    door_y = y + height - door_height

    pygame.draw.rect(surface, door_color, (door_x, door_y, door_width, door_height))

    # Klika
    handle_color = (200, 180, 100)
    pygame.draw.circle(surface, handle_color, (door_x + door_width - 3, door_y + door_height//2), 1)

def add_village_details(surface, size):
    """Přidává vesnicové detaily"""
    # Studna
    if random.random() < 0.4:
        well_x = random.randint(20, size - 20)
        well_y = random.randint(20, size - 20)
        well_color = (120, 120, 120)
        pygame.draw.circle(surface, well_color, (well_x, well_y), 8)
        pygame.draw.circle(surface, (80, 80, 80), (well_x, well_y), 6)

    # Ploty
    for _ in range(random.randint(2, 5)):
        fence_start_x = random.randint(0, size - 30)
        fence_y = random.randint(10, size - 10)
        fence_length = random.randint(15, 30)
        fence_color = (101, 67, 33)

        # Horizontální plot
        pygame.draw.line(surface, fence_color, (fence_start_x, fence_y),
                        (fence_start_x + fence_length, fence_y), 2)

        # Sloupky
        for i in range(0, fence_length, 5):
            pygame.draw.line(surface, fence_color, (fence_start_x + i, fence_y - 3),
                            (fence_start_x + i, fence_y + 3), 1)

    # Malé stromy a keře
    for _ in range(random.randint(3, 8)):
        tree_x = random.randint(10, size - 10)
        tree_y = random.randint(10, size - 10)

        if random.random() < 0.6:
            draw_detailed_tree(surface, tree_x, tree_y, random.randint(6, 12))
        else:
            draw_bush(surface, tree_x, tree_y, random.randint(4, 8))

def create_base_tile(terrain_type):
    """Vytváří základní dlaždici - fallback funkce"""
    return create_detailed_terrain(terrain_type, TILE_SIZE)

def create_detailed_terrain(terrain_type, size=256):
    """Vytváří velmi detailní terén s realistickými objekty"""
    surface = pygame.Surface((size, size))

    if terrain_type == 'grass':
        # Základní tráva s gradientem
        create_grass_base(surface, size)

        # Přidání cest a stezek
        if random.random() < 0.3:
            create_dirt_path(surface, size)

        # Jednotlivé stromy a keře
        for _ in range(random.randint(3, 8)):
            tree_x = random.randint(15, size - 15)
            tree_y = random.randint(15, size - 15)
            if random.random() < 0.7:
                draw_detailed_tree(surface, tree_x, tree_y, random.randint(8, 18))
            else:
                draw_bush(surface, tree_x, tree_y, random.randint(4, 8))

        # Květiny a detaily
        add_grass_details(surface, size)

    elif terrain_type == 'forest':
        # Hustý les s detailním podrostem
        create_forest_base(surface, size)

        # Velké stromy
        for _ in range(random.randint(8, 15)):
            tree_x = random.randint(10, size - 10)
            tree_y = random.randint(10, size - 10)
            draw_detailed_tree(surface, tree_x, tree_y, random.randint(15, 25))

        # Menší stromy a keře
        for _ in range(random.randint(10, 20)):
            x = random.randint(5, size - 5)
            y = random.randint(5, size - 5)
            if random.random() < 0.6:
                draw_detailed_tree(surface, x, y, random.randint(8, 15))
            else:
                draw_bush(surface, x, y, random.randint(6, 12))

        # Lesní stezka
        if random.random() < 0.4:
            create_forest_path(surface, size)

        # Padlé kmeny a detaily
        add_forest_details(surface, size)

    elif terrain_type == 'water':
        # Pokročilá voda s realistickými efekty
        create_water_base(surface, size)
        add_water_effects(surface, size)

        # Břehové detaily
        if random.random() < 0.6:
            add_shoreline_details(surface, size)

    elif terrain_type == 'farmland':
        # Realistické pole s plodinami
        create_farmland_base(surface, size)

        # Typ plodiny
        crop_type = random.choice(['wheat', 'corn', 'vegetables'])
        create_crop_rows(surface, size, crop_type)

        # Zemědělské detaily
        add_farm_details(surface, size)

    elif terrain_type == 'village':
        # Pokročilá vesnice s realistickými detaily
        create_village_base(surface, size)
        create_village_roads(surface, size)
        create_village_buildings(surface, size)
        add_village_details(surface, size)

    elif terrain_type == 'road':
        # Silnice
        base_color = (90, 85, 80)
        surface.fill(base_color)

        # Středová čára
        pygame.draw.rect(surface, (200, 200, 200), (size//2 - 2, 0, 4, size))

        # Okraje silnice
        edge_color = (70, 65, 60)
        pygame.draw.rect(surface, edge_color, (0, 0, 8, size))
        pygame.draw.rect(surface, edge_color, (size - 8, 0, 8, size))

        # Tráva na okrajích
        grass_color = (85, 140, 60)
        for _ in range(20):
            x = random.choice([random.randint(0, 15), random.randint(size - 15, size)])
            y = random.randint(0, size)
            grass_size = random.randint(2, 6)
            pygame.draw.circle(surface, grass_color, (x, y), grass_size)

    else:  # dirt
        # Hlína
        base_color = (139, 115, 85)
        surface.fill(base_color)

        # Kamínky a nerovnosti
        for _ in range(30):
            x = random.randint(0, size)
            y = random.randint(0, size)
            stone_size = random.randint(1, 4)
            stone_color = (base_color[0] + random.randint(-20, 30),
                          base_color[1] + random.randint(-20, 20),
                          base_color[2] + random.randint(-20, 10))
            pygame.draw.circle(surface, stone_color, (x, y), stone_size)

    return surface

def draw_tree(surface, x, y, size):
    """Nakreslí strom na dané pozici"""
    # Kmen
    trunk_color = (101, 67, 33)
    trunk_width = max(2, size // 4)
    trunk_height = size // 2
    pygame.draw.rect(surface, trunk_color, (x - trunk_width//2, y, trunk_width, trunk_height))

    # Koruna
    crown_color = (34, 139, 34)
    crown_size = size
    pygame.draw.circle(surface, crown_color, (x, y), crown_size)

    # Světlejší část koruny
    light_crown = (50, 160, 50)
    pygame.draw.circle(surface, light_crown, (x - crown_size//3, y - crown_size//3), crown_size//2)

def draw_building(surface, x, y, width, height):
    """Nakreslí budovu na dané pozici"""
    # Základní budova
    building_color = (180, 160, 140)
    pygame.draw.rect(surface, building_color, (x, y, width, height))

    # Střecha
    roof_color = (139, 69, 19)
    roof_points = [(x, y), (x + width, y), (x + width//2, y - 10)]
    pygame.draw.polygon(surface, roof_color, roof_points)

    # Okna
    window_color = (100, 150, 200)
    window_size = min(width//4, height//4)
    for i in range(2):
        for j in range(2):
            window_x = x + (i + 1) * width // 3 - window_size//2
            window_y = y + (j + 1) * height // 3 - window_size//2
            pygame.draw.rect(surface, window_color, (window_x, window_y, window_size, window_size))

    # Dveře
    door_color = (101, 67, 33)
    door_width = width // 4
    door_height = height // 2
    door_x = x + width//2 - door_width//2
    door_y = y + height - door_height
    pygame.draw.rect(surface, door_color, (door_x, door_y, door_width, door_height))

def draw_crop(surface, x, y):
    """Nakreslí plodinu na dané pozici"""
    crop_color = (100, 180, 60)
    # Malá rostlinka
    pygame.draw.circle(surface, crop_color, (x, y), 2)
    # Lístky
    pygame.draw.circle(surface, crop_color, (x-1, y-1), 1)
    pygame.draw.circle(surface, crop_color, (x+1, y-1), 1)

def generate_seamless_tileset():
    """Generuje kompletní seamless tileset"""
    # Vytvoření hlavního surface pro tileset
    tileset_surface = pygame.Surface((TILESET_WIDTH * TILE_SIZE, TILESET_HEIGHT * TILE_SIZE))

    # Definice layoutu tilesetů (které dlaždice kam patří)
    tile_layout = {
        # Základní textury
        (0, 0): ('grass', None, None),
        (1, 0): ('dirt', None, None),
        (2, 0): ('water', None, None),
        (3, 0): ('sand', None, None),
        (4, 0): ('stone', None, None),
        (5, 0): ('forest', None, None),
        (6, 0): ('road', None, None),

        # Přechody grass -> dirt
        (0, 1): ('grass', 'dirt', 'right'),
        (1, 1): ('grass', 'dirt', 'bottom'),
        (2, 1): ('grass', 'dirt', 'left'),
        (3, 1): ('grass', 'dirt', 'top'),

        # Přechody grass -> water
        (0, 2): ('grass', 'water', 'right'),
        (1, 2): ('grass', 'water', 'bottom'),
        (2, 2): ('grass', 'water', 'left'),
        (3, 2): ('grass', 'water', 'top'),

        # Přechody dirt -> water
        (0, 3): ('dirt', 'water', 'right'),
        (1, 3): ('dirt', 'water', 'bottom'),
        (2, 3): ('dirt', 'water', 'left'),
        (3, 3): ('dirt', 'water', 'top'),

        # Rohové přechody
        (4, 1): ('grass', 'dirt', 'topright'),
        (5, 1): ('grass', 'dirt', 'bottomright'),
        (6, 1): ('grass', 'dirt', 'bottomleft'),
        (7, 1): ('grass', 'dirt', 'topleft'),
    }

    # Generování jednotlivých dlaždic
    for (tile_x, tile_y), (terrain1, terrain2, direction) in tile_layout.items():
        if terrain2 is None:
            # Základní dlaždice
            tile_surface = create_base_tile(terrain1)
        else:
            # Přechodová dlaždice
            tile_surface = create_transition_tile(terrain1, terrain2, direction)

        # Umístění dlaždice do tilesetů
        tileset_surface.blit(tile_surface, (tile_x * TILE_SIZE, tile_y * TILE_SIZE))

    return tileset_surface

def main():
    """Hlavní funkce"""
    print("Generování realistických terrain textur...")

    # Vytvoření adresáře pro assety
    os.makedirs("assets/images", exist_ok=True)

    # Vytvoření detailních textur pro každý typ terénu
    terrain_types = ['grass', 'forest', 'water', 'farmland', 'village', 'road', 'dirt']

    for terrain_name in terrain_types:
        print(f"Generuji {terrain_name}...")

        # Vytvoření detailní textury
        terrain_texture = create_detailed_terrain(terrain_name, 256)

        # Uložení textury
        pygame.image.save(terrain_texture, f"assets/images/terrain_{terrain_name}.png")
        print(f"Vygenerována terrain_{terrain_name}.png")

    # Vytvoření také základních textur pro kompatibilitu
    basic_terrains = ['sand', 'stone']
    for terrain_name in basic_terrains:
        print(f"Generuji základní {terrain_name}...")
        base_color = TERRAIN_COLORS[terrain_name]
        texture = generate_noise_texture(256, 256, base_color, 20)

        # Přidání detailů
        if terrain_name == 'sand':
            # Písečné duny
            for _ in range(20):
                x = random.randint(0, 256)
                y = random.randint(0, 256)
                dune_size = random.randint(10, 30)
                dune_color = (min(255, base_color[0] + 20), min(255, base_color[1] + 15), base_color[2])
                pygame.draw.circle(texture, dune_color, (x, y), dune_size)

        elif terrain_name == 'stone':
            # Kameny a praskliny
            for _ in range(15):
                x = random.randint(0, 256)
                y = random.randint(0, 256)
                rock_size = random.randint(5, 15)
                rock_color = (base_color[0] + random.randint(-30, 30),
                             base_color[1] + random.randint(-30, 30),
                             base_color[2] + random.randint(-30, 30))
                pygame.draw.circle(texture, rock_color, (x, y), rock_size)

        pygame.image.save(texture, f"assets/images/terrain_{terrain_name}.png")
        print(f"Vygenerována terrain_{terrain_name}.png")

    print("Realistické terrain textury úspěšně vygenerovány!")

if __name__ == "__main__":
    main()
