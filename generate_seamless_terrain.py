#!/usr/bin/env python3
"""
Generátor seamless terrain tilesetů pro hru Spitfire
Vytváří textury, které se plynule navazují na sebe
"""

import pygame
import random
import math
import os

# Inicializace pygame
pygame.init()

# Konstanty
TILE_SIZE = 32
TILESET_WIDTH = 16  # počet dlaždic na šířku
TILESET_HEIGHT = 16  # počet dlaždic na výšku

# Barvy pro různé typy terénu
TERRAIN_COLORS = {
    'grass': (85, 140, 60),
    'dirt': (139, 115, 85),
    'water': (80, 130, 180),
    'sand': (194, 178, 128),
    'stone': (120, 120, 120),
    'forest': (60, 100, 40),
    'road': (90, 85, 80)
}

def generate_noise_texture(width, height, base_color, variation=20):
    """Generuje texturu s šumem pro přirozenější vzhled"""
    surface = pygame.Surface((width, height))
    
    for x in range(width):
        for y in range(height):
            # Perlin-like noise
            noise_value = (
                math.sin(x * 0.1) * math.cos(y * 0.1) * 0.3 +
                math.sin(x * 0.05) * math.cos(y * 0.05) * 0.5 +
                random.uniform(-0.2, 0.2)
            )
            
            # Aplikace šumu na barvu
            r = max(0, min(255, base_color[0] + int(noise_value * variation)))
            g = max(0, min(255, base_color[1] + int(noise_value * variation)))
            b = max(0, min(255, base_color[2] + int(noise_value * variation)))
            
            surface.set_at((x, y), (r, g, b))
    
    return surface

def create_transition_tile(terrain1, terrain2, direction):
    """
    Vytváří přechodovou dlaždici mezi dvěma typy terénu
    direction: 'top', 'bottom', 'left', 'right', 'topleft', 'topright', 'bottomleft', 'bottomright'
    """
    surface = pygame.Surface((TILE_SIZE, TILE_SIZE))
    
    color1 = TERRAIN_COLORS[terrain1]
    color2 = TERRAIN_COLORS[terrain2]
    
    for x in range(TILE_SIZE):
        for y in range(TILE_SIZE):
            # Výpočet blending faktoru podle směru
            if direction == 'top':
                blend = y / TILE_SIZE
            elif direction == 'bottom':
                blend = 1 - (y / TILE_SIZE)
            elif direction == 'left':
                blend = x / TILE_SIZE
            elif direction == 'right':
                blend = 1 - (x / TILE_SIZE)
            elif direction == 'topleft':
                blend = (x + y) / (2 * TILE_SIZE)
            elif direction == 'topright':
                blend = (TILE_SIZE - x + y) / (2 * TILE_SIZE)
            elif direction == 'bottomleft':
                blend = (x + TILE_SIZE - y) / (2 * TILE_SIZE)
            elif direction == 'bottomright':
                blend = (2 * TILE_SIZE - x - y) / (2 * TILE_SIZE)
            else:
                blend = 0.5
            
            # Smooth transition s noise
            noise = random.uniform(-0.1, 0.1)
            blend = max(0, min(1, blend + noise))
            
            # Interpolace barev
            r = int(color1[0] * (1 - blend) + color2[0] * blend)
            g = int(color1[1] * (1 - blend) + color2[1] * blend)
            b = int(color1[2] * (1 - blend) + color2[2] * blend)
            
            surface.set_at((x, y), (r, g, b))
    
    return surface

def create_base_tile(terrain_type):
    """Vytváří základní dlaždici pro daný typ terénu"""
    base_color = TERRAIN_COLORS[terrain_type]
    surface = generate_noise_texture(TILE_SIZE, TILE_SIZE, base_color, 15)
    
    # Přidání specifických detailů podle typu terénu
    if terrain_type == 'grass':
        # Přidání malých tmavších skvrn (tráva)
        for _ in range(random.randint(3, 8)):
            x = random.randint(0, TILE_SIZE - 1)
            y = random.randint(0, TILE_SIZE - 1)
            size = random.randint(1, 3)
            darker_color = (max(0, base_color[0] - 20), base_color[1], max(0, base_color[2] - 10))
            pygame.draw.circle(surface, darker_color, (x, y), size)
    
    elif terrain_type == 'dirt':
        # Přidání malých kamínků
        for _ in range(random.randint(2, 6)):
            x = random.randint(0, TILE_SIZE - 1)
            y = random.randint(0, TILE_SIZE - 1)
            size = random.randint(1, 2)
            stone_color = (base_color[0] + 30, base_color[1] + 20, base_color[2] + 10)
            pygame.draw.circle(surface, stone_color, (x, y), size)
    
    elif terrain_type == 'water':
        # Přidání světelných odlesků
        for _ in range(random.randint(1, 4)):
            x = random.randint(0, TILE_SIZE - 1)
            y = random.randint(0, TILE_SIZE - 1)
            size = random.randint(1, 3)
            light_color = (min(255, base_color[0] + 40), min(255, base_color[1] + 40), min(255, base_color[2] + 40))
            pygame.draw.circle(surface, light_color, (x, y), size)
    
    elif terrain_type == 'forest':
        # Přidání malých tmavých skvrn (stíny stromů)
        for _ in range(random.randint(4, 10)):
            x = random.randint(0, TILE_SIZE - 1)
            y = random.randint(0, TILE_SIZE - 1)
            size = random.randint(2, 5)
            shadow_color = (max(0, base_color[0] - 30), max(0, base_color[1] - 20), max(0, base_color[2] - 20))
            pygame.draw.circle(surface, shadow_color, (x, y), size)
    
    return surface

def generate_seamless_tileset():
    """Generuje kompletní seamless tileset"""
    # Vytvoření hlavního surface pro tileset
    tileset_surface = pygame.Surface((TILESET_WIDTH * TILE_SIZE, TILESET_HEIGHT * TILE_SIZE))
    
    # Definice layoutu tilesetů (které dlaždice kam patří)
    tile_layout = {
        # Základní textury
        (0, 0): ('grass', None, None),
        (1, 0): ('dirt', None, None),
        (2, 0): ('water', None, None),
        (3, 0): ('sand', None, None),
        (4, 0): ('stone', None, None),
        (5, 0): ('forest', None, None),
        (6, 0): ('road', None, None),
        
        # Přechody grass -> dirt
        (0, 1): ('grass', 'dirt', 'right'),
        (1, 1): ('grass', 'dirt', 'bottom'),
        (2, 1): ('grass', 'dirt', 'left'),
        (3, 1): ('grass', 'dirt', 'top'),
        
        # Přechody grass -> water
        (0, 2): ('grass', 'water', 'right'),
        (1, 2): ('grass', 'water', 'bottom'),
        (2, 2): ('grass', 'water', 'left'),
        (3, 2): ('grass', 'water', 'top'),
        
        # Přechody dirt -> water
        (0, 3): ('dirt', 'water', 'right'),
        (1, 3): ('dirt', 'water', 'bottom'),
        (2, 3): ('dirt', 'water', 'left'),
        (3, 3): ('dirt', 'water', 'top'),
        
        # Rohové přechody
        (4, 1): ('grass', 'dirt', 'topright'),
        (5, 1): ('grass', 'dirt', 'bottomright'),
        (6, 1): ('grass', 'dirt', 'bottomleft'),
        (7, 1): ('grass', 'dirt', 'topleft'),
    }
    
    # Generování jednotlivých dlaždic
    for (tile_x, tile_y), (terrain1, terrain2, direction) in tile_layout.items():
        if terrain2 is None:
            # Základní dlaždice
            tile_surface = create_base_tile(terrain1)
        else:
            # Přechodová dlaždice
            tile_surface = create_transition_tile(terrain1, terrain2, direction)
        
        # Umístění dlaždice do tilesetů
        tileset_surface.blit(tile_surface, (tile_x * TILE_SIZE, tile_y * TILE_SIZE))
    
    return tileset_surface

def main():
    """Hlavní funkce"""
    print("Generování seamless terrain tilesetů...")
    
    # Vytvoření adresáře pro assety
    os.makedirs("assets/images", exist_ok=True)
    
    # Generování tilesetů
    tileset = generate_seamless_tileset()
    
    # Uložení
    pygame.image.save(tileset, "assets/images/seamless_terrain.png")
    print("Vygenerován seamless_terrain.png")
    
    # Vytvoření také jednotlivých textur pro background
    for terrain_name in TERRAIN_COLORS.keys():
        tile = create_base_tile(terrain_name)
        # Vytvoření větší textury opakováním
        big_texture = pygame.Surface((256, 256))
        for x in range(0, 256, TILE_SIZE):
            for y in range(0, 256, TILE_SIZE):
                big_texture.blit(tile, (x, y))
        
        pygame.image.save(big_texture, f"assets/images/terrain_{terrain_name}.png")
        print(f"Vygenerována terrain_{terrain_name}.png")
    
    print("Seamless terrain textury úspěšně vygenerovány!")

if __name__ == "__main__":
    main()
