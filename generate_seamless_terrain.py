#!/usr/bin/env python3
"""
Generátor seamless terrain tilesetů pro hru Spitfire
Vytváří textury, které se plynule navazují na sebe
"""

import pygame
import random
import math
import os

# Inicializace pygame
pygame.init()

# Konstanty
TILE_SIZE = 32
TILESET_WIDTH = 16  # počet dlaždic na šířku
TILESET_HEIGHT = 16  # počet dlaždic na výšku

# Barvy pro různé typy terénu
TERRAIN_COLORS = {
    'grass': (85, 140, 60),
    'dirt': (139, 115, 85),
    'water': (80, 130, 180),
    'sand': (194, 178, 128),
    'stone': (120, 120, 120),
    'forest': (60, 100, 40),
    'road': (90, 85, 80),
    'farmland': (160, 140, 80),
    'village': (120, 100, 80)
}

def generate_noise_texture(width, height, base_color, variation=20):
    """Generuje texturu s šumem pro přirozenější vzhled"""
    surface = pygame.Surface((width, height))

    for x in range(width):
        for y in range(height):
            # Perlin-like noise
            noise_value = (
                math.sin(x * 0.1) * math.cos(y * 0.1) * 0.3 +
                math.sin(x * 0.05) * math.cos(y * 0.05) * 0.5 +
                random.uniform(-0.2, 0.2)
            )

            # Aplikace šumu na barvu
            r = max(0, min(255, base_color[0] + int(noise_value * variation)))
            g = max(0, min(255, base_color[1] + int(noise_value * variation)))
            b = max(0, min(255, base_color[2] + int(noise_value * variation)))

            surface.set_at((x, y), (r, g, b))

    return surface

def create_transition_tile(terrain1, terrain2, direction):
    """
    Vytváří přechodovou dlaždici mezi dvěma typy terénu
    direction: 'top', 'bottom', 'left', 'right', 'topleft', 'topright', 'bottomleft', 'bottomright'
    """
    surface = pygame.Surface((TILE_SIZE, TILE_SIZE))

    color1 = TERRAIN_COLORS[terrain1]
    color2 = TERRAIN_COLORS[terrain2]

    for x in range(TILE_SIZE):
        for y in range(TILE_SIZE):
            # Výpočet blending faktoru podle směru
            if direction == 'top':
                blend = y / TILE_SIZE
            elif direction == 'bottom':
                blend = 1 - (y / TILE_SIZE)
            elif direction == 'left':
                blend = x / TILE_SIZE
            elif direction == 'right':
                blend = 1 - (x / TILE_SIZE)
            elif direction == 'topleft':
                blend = (x + y) / (2 * TILE_SIZE)
            elif direction == 'topright':
                blend = (TILE_SIZE - x + y) / (2 * TILE_SIZE)
            elif direction == 'bottomleft':
                blend = (x + TILE_SIZE - y) / (2 * TILE_SIZE)
            elif direction == 'bottomright':
                blend = (2 * TILE_SIZE - x - y) / (2 * TILE_SIZE)
            else:
                blend = 0.5

            # Smooth transition s noise
            noise = random.uniform(-0.1, 0.1)
            blend = max(0, min(1, blend + noise))

            # Interpolace barev
            r = int(color1[0] * (1 - blend) + color2[0] * blend)
            g = int(color1[1] * (1 - blend) + color2[1] * blend)
            b = int(color1[2] * (1 - blend) + color2[2] * blend)

            surface.set_at((x, y), (r, g, b))

    return surface

def create_detailed_terrain(terrain_type, size=256):
    """Vytváří detailní terén s realistickými objekty"""
    surface = pygame.Surface((size, size))

    if terrain_type == 'grass':
        # Základní tráva
        base_color = (85, 140, 60)
        surface.fill(base_color)

        # Přidání různých odstínů trávy
        for _ in range(50):
            x = random.randint(0, size)
            y = random.randint(0, size)
            patch_size = random.randint(10, 30)
            variation = random.randint(-20, 20)
            patch_color = (
                max(30, min(255, base_color[0] + variation)),
                max(30, min(255, base_color[1] + variation)),
                max(30, min(255, base_color[2] + variation))
            )
            pygame.draw.circle(surface, patch_color, (x, y), patch_size)

        # Přidání jednotlivých stromů
        for _ in range(random.randint(2, 6)):
            tree_x = random.randint(10, size - 10)
            tree_y = random.randint(10, size - 10)
            draw_tree(surface, tree_x, tree_y, random.randint(8, 15))

    elif terrain_type == 'forest':
        # Hustý les
        base_color = (60, 100, 40)
        surface.fill(base_color)

        # Mnoho stromů různých velikostí
        for _ in range(random.randint(15, 25)):
            tree_x = random.randint(5, size - 5)
            tree_y = random.randint(5, size - 5)
            tree_size = random.randint(6, 20)
            draw_tree(surface, tree_x, tree_y, tree_size)

        # Stíny mezi stromy
        for _ in range(20):
            x = random.randint(0, size)
            y = random.randint(0, size)
            shadow_size = random.randint(5, 15)
            shadow_color = (max(0, base_color[0] - 30), max(0, base_color[1] - 20), max(0, base_color[2] - 20))
            pygame.draw.circle(surface, shadow_color, (x, y), shadow_size)

    elif terrain_type == 'water':
        # Řeka nebo jezero
        base_color = (80, 130, 180)
        surface.fill(base_color)

        # Vlnky na vodě
        for _ in range(30):
            x = random.randint(0, size)
            y = random.randint(0, size)
            wave_length = random.randint(20, 60)
            wave_color = (min(255, base_color[0] + 30), min(255, base_color[1] + 30), min(255, base_color[2] + 30))
            pygame.draw.ellipse(surface, wave_color, (x, y, wave_length, 3))

        # Odlesky světla
        for _ in range(10):
            x = random.randint(0, size)
            y = random.randint(0, size)
            light_size = random.randint(3, 8)
            light_color = (min(255, base_color[0] + 60), min(255, base_color[1] + 60), min(255, base_color[2] + 60))
            pygame.draw.circle(surface, light_color, (x, y), light_size)

    elif terrain_type == 'farmland':
        # Pole s plodinami
        base_color = (160, 140, 80)
        surface.fill(base_color)

        # Řádky plodin
        row_color = (120, 180, 70)
        for y in range(0, size, 12):
            pygame.draw.rect(surface, row_color, (0, y, size, 8))

            # Jednotlivé rostliny
            for x in range(0, size, 15):
                plant_x = x + random.randint(-3, 3)
                plant_y = y + random.randint(-2, 2)
                if 0 <= plant_x < size and 0 <= plant_y < size:
                    draw_crop(surface, plant_x, plant_y)

    elif terrain_type == 'village':
        # Vesnice s budovami
        base_color = (120, 100, 80)
        surface.fill(base_color)

        # Budovy
        for _ in range(random.randint(3, 8)):
            building_x = random.randint(10, size - 40)
            building_y = random.randint(10, size - 30)
            building_width = random.randint(25, 40)
            building_height = random.randint(20, 30)
            draw_building(surface, building_x, building_y, building_width, building_height)

        # Cesty mezi budovami
        road_color = (90, 85, 80)
        pygame.draw.rect(surface, road_color, (0, size//2 - 5, size, 10))
        pygame.draw.rect(surface, road_color, (size//2 - 5, 0, 10, size))

    elif terrain_type == 'road':
        # Silnice
        base_color = (90, 85, 80)
        surface.fill(base_color)

        # Středová čára
        pygame.draw.rect(surface, (200, 200, 200), (size//2 - 2, 0, 4, size))

        # Okraje silnice
        edge_color = (70, 65, 60)
        pygame.draw.rect(surface, edge_color, (0, 0, 8, size))
        pygame.draw.rect(surface, edge_color, (size - 8, 0, 8, size))

        # Tráva na okrajích
        grass_color = (85, 140, 60)
        for _ in range(20):
            x = random.choice([random.randint(0, 15), random.randint(size - 15, size)])
            y = random.randint(0, size)
            grass_size = random.randint(2, 6)
            pygame.draw.circle(surface, grass_color, (x, y), grass_size)

    else:  # dirt
        # Hlína
        base_color = (139, 115, 85)
        surface.fill(base_color)

        # Kamínky a nerovnosti
        for _ in range(30):
            x = random.randint(0, size)
            y = random.randint(0, size)
            stone_size = random.randint(1, 4)
            stone_color = (base_color[0] + random.randint(-20, 30),
                          base_color[1] + random.randint(-20, 20),
                          base_color[2] + random.randint(-20, 10))
            pygame.draw.circle(surface, stone_color, (x, y), stone_size)

    return surface

def draw_tree(surface, x, y, size):
    """Nakreslí strom na dané pozici"""
    # Kmen
    trunk_color = (101, 67, 33)
    trunk_width = max(2, size // 4)
    trunk_height = size // 2
    pygame.draw.rect(surface, trunk_color, (x - trunk_width//2, y, trunk_width, trunk_height))

    # Koruna
    crown_color = (34, 139, 34)
    crown_size = size
    pygame.draw.circle(surface, crown_color, (x, y), crown_size)

    # Světlejší část koruny
    light_crown = (50, 160, 50)
    pygame.draw.circle(surface, light_crown, (x - crown_size//3, y - crown_size//3), crown_size//2)

def draw_building(surface, x, y, width, height):
    """Nakreslí budovu na dané pozici"""
    # Základní budova
    building_color = (180, 160, 140)
    pygame.draw.rect(surface, building_color, (x, y, width, height))

    # Střecha
    roof_color = (139, 69, 19)
    roof_points = [(x, y), (x + width, y), (x + width//2, y - 10)]
    pygame.draw.polygon(surface, roof_color, roof_points)

    # Okna
    window_color = (100, 150, 200)
    window_size = min(width//4, height//4)
    for i in range(2):
        for j in range(2):
            window_x = x + (i + 1) * width // 3 - window_size//2
            window_y = y + (j + 1) * height // 3 - window_size//2
            pygame.draw.rect(surface, window_color, (window_x, window_y, window_size, window_size))

    # Dveře
    door_color = (101, 67, 33)
    door_width = width // 4
    door_height = height // 2
    door_x = x + width//2 - door_width//2
    door_y = y + height - door_height
    pygame.draw.rect(surface, door_color, (door_x, door_y, door_width, door_height))

def draw_crop(surface, x, y):
    """Nakreslí plodinu na dané pozici"""
    crop_color = (100, 180, 60)
    # Malá rostlinka
    pygame.draw.circle(surface, crop_color, (x, y), 2)
    # Lístky
    pygame.draw.circle(surface, crop_color, (x-1, y-1), 1)
    pygame.draw.circle(surface, crop_color, (x+1, y-1), 1)

def generate_seamless_tileset():
    """Generuje kompletní seamless tileset"""
    # Vytvoření hlavního surface pro tileset
    tileset_surface = pygame.Surface((TILESET_WIDTH * TILE_SIZE, TILESET_HEIGHT * TILE_SIZE))

    # Definice layoutu tilesetů (které dlaždice kam patří)
    tile_layout = {
        # Základní textury
        (0, 0): ('grass', None, None),
        (1, 0): ('dirt', None, None),
        (2, 0): ('water', None, None),
        (3, 0): ('sand', None, None),
        (4, 0): ('stone', None, None),
        (5, 0): ('forest', None, None),
        (6, 0): ('road', None, None),

        # Přechody grass -> dirt
        (0, 1): ('grass', 'dirt', 'right'),
        (1, 1): ('grass', 'dirt', 'bottom'),
        (2, 1): ('grass', 'dirt', 'left'),
        (3, 1): ('grass', 'dirt', 'top'),

        # Přechody grass -> water
        (0, 2): ('grass', 'water', 'right'),
        (1, 2): ('grass', 'water', 'bottom'),
        (2, 2): ('grass', 'water', 'left'),
        (3, 2): ('grass', 'water', 'top'),

        # Přechody dirt -> water
        (0, 3): ('dirt', 'water', 'right'),
        (1, 3): ('dirt', 'water', 'bottom'),
        (2, 3): ('dirt', 'water', 'left'),
        (3, 3): ('dirt', 'water', 'top'),

        # Rohové přechody
        (4, 1): ('grass', 'dirt', 'topright'),
        (5, 1): ('grass', 'dirt', 'bottomright'),
        (6, 1): ('grass', 'dirt', 'bottomleft'),
        (7, 1): ('grass', 'dirt', 'topleft'),
    }

    # Generování jednotlivých dlaždic
    for (tile_x, tile_y), (terrain1, terrain2, direction) in tile_layout.items():
        if terrain2 is None:
            # Základní dlaždice
            tile_surface = create_base_tile(terrain1)
        else:
            # Přechodová dlaždice
            tile_surface = create_transition_tile(terrain1, terrain2, direction)

        # Umístění dlaždice do tilesetů
        tileset_surface.blit(tile_surface, (tile_x * TILE_SIZE, tile_y * TILE_SIZE))

    return tileset_surface

def main():
    """Hlavní funkce"""
    print("Generování realistických terrain textur...")

    # Vytvoření adresáře pro assety
    os.makedirs("assets/images", exist_ok=True)

    # Vytvoření detailních textur pro každý typ terénu
    terrain_types = ['grass', 'forest', 'water', 'farmland', 'village', 'road', 'dirt']

    for terrain_name in terrain_types:
        print(f"Generuji {terrain_name}...")

        # Vytvoření detailní textury
        terrain_texture = create_detailed_terrain(terrain_name, 256)

        # Uložení textury
        pygame.image.save(terrain_texture, f"assets/images/terrain_{terrain_name}.png")
        print(f"Vygenerována terrain_{terrain_name}.png")

    # Vytvoření také základních textur pro kompatibilitu
    basic_terrains = ['sand', 'stone']
    for terrain_name in basic_terrains:
        print(f"Generuji základní {terrain_name}...")
        base_color = TERRAIN_COLORS[terrain_name]
        texture = generate_noise_texture(256, 256, base_color, 20)

        # Přidání detailů
        if terrain_name == 'sand':
            # Písečné duny
            for _ in range(20):
                x = random.randint(0, 256)
                y = random.randint(0, 256)
                dune_size = random.randint(10, 30)
                dune_color = (min(255, base_color[0] + 20), min(255, base_color[1] + 15), base_color[2])
                pygame.draw.circle(texture, dune_color, (x, y), dune_size)

        elif terrain_name == 'stone':
            # Kameny a praskliny
            for _ in range(15):
                x = random.randint(0, 256)
                y = random.randint(0, 256)
                rock_size = random.randint(5, 15)
                rock_color = (base_color[0] + random.randint(-30, 30),
                             base_color[1] + random.randint(-30, 30),
                             base_color[2] + random.randint(-30, 30))
                pygame.draw.circle(texture, rock_color, (x, y), rock_size)

        pygame.image.save(texture, f"assets/images/terrain_{terrain_name}.png")
        print(f"Vygenerována terrain_{terrain_name}.png")

    print("Realistické terrain textury úspěšně vygenerovány!")

if __name__ == "__main__":
    main()
