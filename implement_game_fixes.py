#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Spitfire Game Fixes Implementer
------------------------------
This script helps implement fixes to the Spitfire game based on feedback.
"""

import os
import sys
import re
import shutil

def backup_game_file():
    """Create a backup of the game file."""
    game_file = "spitfire_game.py"
    backup_file = "spitfire_game.py.backup"
    
    if not os.path.exists(game_file):
        print(f"Error: Game file {game_file} not found.")
        return False
    
    try:
        shutil.copy2(game_file, backup_file)
        print(f"Created backup of game file: {backup_file}")
        return True
    except Exception as e:
        print(f"Error creating backup: {e}")
        return False

def read_game_file():
    """Read the game file content."""
    game_file = "spitfire_game.py"
    
    try:
        with open(game_file, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except Exception as e:
        print(f"Error reading game file: {e}")
        return None

def write_game_file(content):
    """Write content to the game file."""
    game_file = "spitfire_game.py"
    
    try:
        with open(game_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Updated game file: {game_file}")
        return True
    except Exception as e:
        print(f"Error writing game file: {e}")
        return False

def implement_enemy_shooting(content):
    """Implement enemy shooting functionality."""
    # Check if EnemyBullet class already exists
    if "class EnemyBullet" in content:
        print("Enemy bullet class already exists, skipping...")
    else:
        # Find the Bullet class and add EnemyBullet after it
        bullet_class_match = re.search(r'class Bullet\(pygame\.sprite\.Sprite\):.*?def update\(self\):.*?(?=class|def|\n\n)', content, re.DOTALL)
        
        if bullet_class_match:
            bullet_class = bullet_class_match.group(0)
            enemy_bullet_class = """
class EnemyBullet(pygame.sprite.Sprite):
    def __init__(self, x, y):
        pygame.sprite.Sprite.__init__(self)
        self.image = enemy_bullet_img
        self.rect = self.image.get_rect()
        self.rect.centerx = x
        self.rect.top = y
        self.speedy = 10  # Pohybuje se dolů (opačně než hráčova střela)

    def update(self):
        self.rect.y += self.speedy
        # Odstranění střely, když opustí obrazovku
        if self.rect.top > HEIGHT:
            self.kill()
"""
            content = content.replace(bullet_class, bullet_class + enemy_bullet_class)
            print("Added EnemyBullet class")
        else:
            print("Could not find Bullet class to add EnemyBullet")
    
    # Add enemy shooting method to Enemy class
    if "def shoot(self):" in content and "enemy_bullets.add" in content:
        print("Enemy shoot method already exists, skipping...")
    else:
        # Find the Enemy class update method
        enemy_update_match = re.search(r'def update\(self\):(.*?)(?=def|\n\n|class)', content, re.DOTALL)
        
        if enemy_update_match:
            enemy_update = enemy_update_match.group(0)
            shoot_method = """
    def shoot(self):
        # Náhodná šance na výstřel
        if random.random() < 0.01:  # 1% šance na výstřel v každém snímku
            bullet = EnemyBullet(self.rect.centerx, self.rect.bottom)
            all_sprites.add(bullet)
            enemy_bullets.add(bullet)
            if shoot_sound:
                shoot_sound.play()
"""
            # Add shoot method call at the end of update method
            shoot_call = "        self.shoot()  # Možnost střelby\n"
            updated_update = enemy_update.rstrip() + "\n" + shoot_call
            content = content.replace(enemy_update, updated_update)
            
            # Add shoot method after the update method
            enemy_class_match = re.search(r'class Enemy\(pygame\.sprite\.Sprite\):.*?(?=class|\Z)', content, re.DOTALL)
            if enemy_class_match:
                enemy_class = enemy_class_match.group(0)
                updated_enemy_class = enemy_class.rstrip() + shoot_method + "\n\n"
                content = content.replace(enemy_class, updated_enemy_class)
                print("Added shoot method to Enemy class")
            else:
                print("Could not find Enemy class to add shoot method")
        else:
            print("Could not find Enemy update method")
    
    # Add enemy_bullets sprite group
    if "enemy_bullets = pygame.sprite.Group()" in content:
        print("Enemy bullets sprite group already exists, skipping...")
    else:
        # Find where sprite groups are defined
        sprite_groups_match = re.search(r'all_sprites = pygame\.sprite\.Group\(\).*?enemies = pygame\.sprite\.Group\(\)', content, re.DOTALL)
        
        if sprite_groups_match:
            sprite_groups = sprite_groups_match.group(0)
            updated_sprite_groups = sprite_groups + "\nenemy_bullets = pygame.sprite.Group()"
            content = content.replace(sprite_groups, updated_sprite_groups)
            print("Added enemy_bullets sprite group")
        else:
            print("Could not find sprite groups to add enemy_bullets")
    
    # Add enemy bullet image loading
    if "enemy_bullet_img =" in content:
        print("Enemy bullet image loading already exists, skipping...")
    else:
        # Find where bullet image is loaded
        bullet_img_match = re.search(r'bullet_img =.*?load_image\("bullet\.png"\)', content)
        
        if bullet_img_match:
            bullet_img = bullet_img_match.group(0)
            enemy_bullet_img = "\nenemy_bullet_img = load_image(\"enemy_bullet.png\")"
            content = content.replace(bullet_img, bullet_img + enemy_bullet_img)
            print("Added enemy bullet image loading")
        else:
            print("Could not find bullet image loading to add enemy bullet")
    
    # Add collision detection for player with enemy bullets
    if "pygame.sprite.spritecollide(player, enemy_bullets," in content:
        print("Player-enemy bullet collision already exists, skipping...")
    else:
        # Find player-enemy collision
        player_collision_match = re.search(r'hits = pygame\.sprite\.spritecollide\(player, enemies,.*?\)', content)
        
        if player_collision_match:
            player_collision = player_collision_match.group(0)
            enemy_bullet_collision = "\n    # Kontrola kolize hráče s nepřátelskými střelami\n    hits = pygame.sprite.spritecollide(player, enemy_bullets, True)\n    for hit in hits:\n        player.health -= 10\n        if explosion_sound:\n            explosion_sound.play()\n        if player.health <= 0:\n            running = False"
            
            # Find the game loop where collisions are checked
            game_loop_match = re.search(r'while running:.*?for event in pygame\.event\.get\(\):', content, re.DOTALL)
            
            if game_loop_match:
                game_loop = game_loop_match.group(0)
                # Find a good place to insert the collision code
                clock_tick_match = re.search(r'clock\.tick\(FPS\)', content)
                
                if clock_tick_match:
                    clock_tick = clock_tick_match.group(0)
                    updated_content = content.replace(clock_tick, clock_tick + enemy_bullet_collision)
                    content = updated_content
                    print("Added player-enemy bullet collision detection")
                else:
                    print("Could not find clock.tick to add collision detection")
            else:
                print("Could not find game loop to add collision detection")
        else:
            print("Could not find player-enemy collision to add enemy bullet collision")
    
    return content

def implement_boss_logic(content):
    """Implement boss logic with screen stopping."""
    # Add boss_active flag
    if "boss_active = False" in content:
        print("Boss active flag already exists, skipping...")
    else:
        # Find where game variables are initialized
        game_vars_match = re.search(r'score = 0.*?level_complete = False', content, re.DOTALL)
        
        if game_vars_match:
            game_vars = game_vars_match.group(0)
            updated_game_vars = game_vars + "\nboss_active = False  # Flag for when boss is active"
            content = content.replace(game_vars, updated_game_vars)
            print("Added boss_active flag")
        else:
            print("Could not find game variables to add boss_active flag")
    
    # Modify Boss class to have better movement and shooting
    if "def shoot(self):" in content and "boss_bullets.add" in content:
        print("Boss shoot method already exists, skipping...")
    else:
        # Find the Boss class
        boss_class_match = re.search(r'class Boss\(pygame\.sprite\.Sprite\):.*?(?=class|\Z)', content, re.DOTALL)
        
        if boss_class_match:
            boss_class = boss_class_match.group(0)
            
            # Check if Boss has an __init__ method
            boss_init_match = re.search(r'def __init__\(self\):.*?(?=def|\n\n|$)', boss_class, re.DOTALL)
            
            if boss_init_match:
                boss_init = boss_init_match.group(0)
                # Add speed attribute if it doesn't exist
                if "self.speed =" not in boss_init:
                    updated_boss_init = boss_init.rstrip() + "\n        self.speed = 2  # Horizontal movement speed\n        self.health = 100  # Boss has more health\n"
                    boss_class = boss_class.replace(boss_init, updated_boss_init)
                    print("Updated Boss __init__ method")
            
            # Check if Boss has an update method
            boss_update_match = re.search(r'def update\(self\):.*?(?=def|\n\n|$)', boss_class, re.DOTALL)
            
            if boss_update_match:
                boss_update = boss_update_match.group(0)
                # Replace with improved update method
                improved_update = """
    def update(self):
        # Pohyb zleva doprava
        self.rect.x += self.speed
        
        # Změna směru při dosažení okraje
        if self.rect.right > WIDTH - 20 or self.rect.left < 20:
            self.speed *= -1
            
        # Střelba
        if random.random() < 0.05:  # 5% šance na výstřel
            self.shoot()
"""
                boss_class = boss_class.replace(boss_update, improved_update)
                print("Updated Boss update method")
            else:
                # Add update method if it doesn't exist
                improved_update = """
    def update(self):
        # Pohyb zleva doprava
        self.rect.x += self.speed
        
        # Změna směru při dosažení okraje
        if self.rect.right > WIDTH - 20 or self.rect.left < 20:
            self.speed *= -1
            
        # Střelba
        if random.random() < 0.05:  # 5% šance na výstřel
            self.shoot()
"""
                boss_class = boss_class.rstrip() + improved_update
                print("Added Boss update method")
            
            # Add shoot method
            if "def shoot(self):" not in boss_class:
                shoot_method = """
    def shoot(self):
        # Boss střílí tři střely najednou
        bullet1 = EnemyBullet(self.rect.centerx - 20, self.rect.bottom)
        bullet2 = EnemyBullet(self.rect.centerx, self.rect.bottom)
        bullet3 = EnemyBullet(self.rect.centerx + 20, self.rect.bottom)
        
        all_sprites.add(bullet1)
        all_sprites.add(bullet2)
        all_sprites.add(bullet3)
        
        enemy_bullets.add(bullet1)
        enemy_bullets.add(bullet2)
        enemy_bullets.add(bullet3)
        
        if shoot_sound:
            shoot_sound.play()
"""
                boss_class = boss_class.rstrip() + shoot_method + "\n\n"
                print("Added Boss shoot method")
            
            content = content.replace(boss_class_match.group(0), boss_class)
        else:
            print("Could not find Boss class")
    
    # Add code to stop background when boss is active
    if "if boss_active:" in content and "background_speed = 0" in content:
        print("Background stopping for boss already exists, skipping...")
    else:
        # Find where background is scrolled
        background_scroll_match = re.search(r'background_y \+= \d+', content)
        
        if background_scroll_match:
            background_scroll = background_scroll_match.group(0)
            speed_value = re.search(r'\d+', background_scroll).group(0)
            
            boss_background_code = f"""
    # Kontrola, zda je boss aktivní - pokud ano, zastav pozadí
    if boss_active:
        background_speed = 0
    else:
        background_speed = {speed_value}
        
    background_y += background_speed"""
            
            content = content.replace(background_scroll, boss_background_code)
            print("Added background stopping for boss")
        else:
            print("Could not find background scrolling code")
    
    # Add code to set boss_active flag when boss appears
    if "boss_active = True" in content:
        print("Boss activation code already exists, skipping...")
    else:
        # Find where boss is created
        boss_creation_match = re.search(r'boss = Boss\(\).*?all_sprites\.add\(boss\)', content, re.DOTALL)
        
        if boss_creation_match:
            boss_creation = boss_creation_match.group(0)
            updated_boss_creation = boss_creation + "\n    boss_active = True  # Activate boss mode"
            content = content.replace(boss_creation, updated_boss_creation)
            print("Added boss activation code")
        else:
            print("Could not find boss creation code")
    
    # Add code to reset boss_active flag when boss is defeated
    if "boss_active = False" in content and "boss.health <= 0" in content:
        print("Boss deactivation code already exists, skipping...")
    else:
        # Find where boss collision is checked
        boss_collision_match = re.search(r'hits = pygame\.sprite\.spritecollide\(boss,.*?bullets.*?\)', content)
        
        if boss_collision_match:
            # Find the block where boss is hit
            boss_hit_block_match = re.search(r'hits = pygame\.sprite\.spritecollide\(boss,.*?bullets.*?\).*?(?=\n\s*\n|\Z)', content, re.DOTALL)
            
            if boss_hit_block_match:
                boss_hit_block = boss_hit_block_match.group(0)
                
                # Check if there's already health tracking
                if "boss.health" in boss_hit_block:
                    # Add deactivation code if boss health check exists
                    if "boss.health <= 0" in boss_hit_block and "boss_active = False" not in boss_hit_block:
                        # Find the boss health check
                        boss_health_check_match = re.search(r'if boss\.health <= 0:.*?(?=\n\s*\n|\Z)', boss_hit_block, re.DOTALL)
                        
                        if boss_health_check_match:
                            boss_health_check = boss_health_check_match.group(0)
                            updated_boss_health_check = boss_health_check.rstrip() + "\n            boss_active = False  # Deactivate boss mode\n"
                            content = content.replace(boss_health_check, updated_boss_health_check)
                            print("Added boss deactivation code")
                        else:
                            print("Could not find boss health check")
                else:
                    # Add health tracking and deactivation
                    updated_boss_hit_block = boss_hit_block.rstrip() + """
        # Decrease boss health
        boss.health -= 10
        if boss.health <= 0:
            boss.kill()
            score += 1000  # Big score for defeating boss
            boss_active = False  # Deactivate boss mode
            # Create explosion
            explosion = Explosion(boss.rect.center)
            all_sprites.add(explosion)
            if explosion_sound:
                explosion_sound.play()
"""
                    content = content.replace(boss_hit_block, updated_boss_hit_block)
                    print("Added boss health tracking and deactivation code")
            else:
                print("Could not find boss hit block")
        else:
            print("Could not find boss collision check")
    
    return content

def implement_level_fixes(content):
    """Implement fixes for level progression and enemy generation."""
    # Fix enemy generation in second level
    if "if current_level == 2:" in content and "enemy = Enemy()" in content:
        print("Level 2 enemy generation already exists, skipping...")
    else:
        # Find where enemies are generated
        enemy_gen_match = re.search(r'if random\.random\(\) < 0\.\d+:.*?enemy = Enemy\(\)', content, re.DOTALL)
        
        if enemy_gen_match:
            enemy_gen = enemy_gen_match.group(0)
            
            # Check if it's already in a level check
            if "if current_level ==" in enemy_gen:
                print("Enemy generation already has level checks, skipping...")
            else:
                # Wrap in level check and add level 2 with higher probability
                level_enemy_gen = f"""
    # Enemy generation based on level
    if current_level == 1:
        {enemy_gen.strip()}
    elif current_level == 2:
        if random.random() < 0.03:  # Higher probability in level 2
            enemy = Enemy()
            # Make enemies faster in level 2
            enemy.speedy += 1
"""
                content = content.replace(enemy_gen, level_enemy_gen)
                print("Added level-based enemy generation")
        else:
            print("Could not find enemy generation code")
    
    return content

def implement_ground_object_fixes(content):
    """Implement fixes for ground objects (airfield, allies, buildings)."""
    # Fix ground objects to move with background
    ground_objects = ["Airfield", "Ally", "Building", "Tank"]
    
    for obj in ground_objects:
        # Check if the class exists
        class_match = re.search(f'class {obj}\(pygame\.sprite\.Sprite\):.*?(?=class|\Z)', content, re.DOTALL)
        
        if class_match:
            class_content = class_match.group(0)
            
            # Check if update method exists and has proper movement
            update_match = re.search(r'def update\(self\):.*?(?=def|\n\n|$)', class_content, re.DOTALL)
            
            if update_match:
                update_method = update_match.group(0)
                
                # Check if it already moves with background
                if "background_speed" in update_method:
                    print(f"{obj} already moves with background, skipping...")
                else:
                    # Replace with proper movement
                    new_update = """
    def update(self):
        # Move with background
        if 'background_speed' in globals():
            self.rect.y += background_speed
        else:
            self.rect.y += 2  # Default speed if background_speed not defined
            
        # Remove when off screen
        if self.rect.top > HEIGHT:
            self.kill()
"""
                    class_content = class_content.replace(update_method, new_update)
                    content = content.replace(class_match.group(0), class_content)
                    print(f"Updated {obj} to move with background")
            else:
                # Add update method
                new_update = """
    def update(self):
        # Move with background
        if 'background_speed' in globals():
            self.rect.y += background_speed
        else:
            self.rect.y += 2  # Default speed if background_speed not defined
            
        # Remove when off screen
        if self.rect.top > HEIGHT:
            self.kill()
"""
                class_content = class_content.rstrip() + new_update + "\n\n"
                content = content.replace(class_match.group(0), class_content)
                print(f"Added update method to {obj}")
        else:
            print(f"Could not find {obj} class")
    
    # Add background_speed global variable if it doesn't exist
    if "background_speed = " not in content:
        # Find where background_y is defined
        background_y_match = re.search(r'background_y = 0', content)
        
        if background_y_match:
            background_y = background_y_match.group(0)
            updated_background_y = background_y + "\nbackground_speed = 2  # Speed at which background scrolls"
            content = content.replace(background_y, updated_background_y)
            print("Added background_speed variable")
        else:
            print("Could not find background_y to add background_speed")
    
    return content

def fix_music_loading(content):
    """Fix music loading to work with both MP3 and WAV."""
    # Check if music loading already has fallback
    if "try:" in content and "game_music = pygame.mixer.Sound" in content:
        print("Music loading already has fallback, skipping...")
    else:
        # Find where music is loaded
        music_load_match = re.search(r'game_music = pygame\.mixer\.Sound\(.*?\)', content)
        
        if music_load_match:
            music_load = music_load_match.group(0)
            
            # Replace with try/except block that tries both MP3 and WAV
            new_music_load = """
try:
    # Try MP3 first
    game_music = pygame.mixer.Sound("assets/music/game_music.mp3")
except:
    try:
        # Fall back to WAV
        game_music = pygame.mixer.Sound("assets/music/game_music.wav")
    except:
        print("Hudební soubor nebyl nalezen. Hra bude bez hudby.")
        game_music = None
"""
            content = content.replace(music_load, new_music_load)
            print("Updated music loading with fallback")
        else:
            print("Could not find music loading code")
    
    # Add music replay logic
    if "if not pygame.mixer.get_busy()" in content and "game_music.play()" in content:
        print("Music replay logic already exists, skipping...")
    else:
        # Find the game loop
        game_loop_match = re.search(r'while running:.*?clock\.tick\(FPS\)', content, re.DOTALL)
        
        if game_loop_match:
            game_loop = game_loop_match.group(0)
            
            # Add music replay check
            music_replay = """
    # Check if music is playing, restart if not
    if game_music and not pygame.mixer.get_busy():
        game_music.play()
"""
            updated_game_loop = game_loop + music_replay
            content = content.replace(game_loop, updated_game_loop)
            print("Added music replay logic")
        else:
            print("Could not find game loop to add music replay")
    
    return content

def main():
    """Main function to implement all fixes."""
    print("Implementing fixes to Spitfire game...")
    
    # Backup the game file
    if not backup_game_file():
        print("Aborting due to backup failure.")
        return
    
    # Read the game file
    content = read_game_file()
    if content is None:
        print("Aborting due to file read failure.")
        return
    
    # Implement fixes
    content = implement_enemy_shooting(content)
    content = implement_boss_logic(content)
    content = implement_level_fixes(content)
    content = implement_ground_object_fixes(content)
    content = fix_music_loading(content)
    
    # Write the updated content
    if not write_game_file(content):
        print("Aborting due to file write failure.")
        return
    
    print("\nAll fixes have been implemented!")
    print("Please run the game to test the changes.")
    print("If there are any issues, the original game file has been backed up as spitfire_game.py.backup")

if __name__ == "__main__":
    main()