#!/usr/bin/env python3
"""
SPITFIRE - Vylepšen<PERSON> verze s novými systémy
"""

import pygame
import sys
import math
import random
import json
import os
import time
from enum import Enum

# Konstanty
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Barvy
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)

class GameState(Enum):
    MENU = "menu"
    PLAYING = "playing"
    PAUSED = "paused"
    GAME_OVER = "game_over"
    SETTINGS = "settings"

class ImprovedSpitfireGame:
    """Hlavní třída vylepšené hry"""

    def __init__(self):
        pygame.init()
        pygame.mixer.init()

        # Základní nastavení
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Spitfire - Vylepšená verze")
        self.clock = pygame.time.Clock()

        # Herní stav
        self.running = True
        self.current_state = GameState.MENU

        # Statistiky
        self.frame_count = 0
        self.start_time = pygame.time.get_ticks()
        self.fps_history = []

        # Herní objekty
        self.player_pos = [SCREEN_WIDTH//2, SCREEN_HEIGHT//2]
        self.player_health = 100
        self.enemies = []
        self.bullets = []
        self.enemy_bullets = []
        self.score = 0
        self.level = 1

        # Načtení grafiky a zvuků
        self.load_assets()

        # AI systém
        self.enemy_spawn_timer = 0
        self.formation_timer = 0

        # Menu
        self.menu_selection = 0
        self.menu_options = ["Nová hra", "Nastavení", "Konec"]

        # Nastavení hlasitosti
        self.master_volume = 0.3  # Snížená výchozí hlasitost
        self.music_volume = 0.2
        self.sfx_volume = 0.4

        # Nastavení obtížnosti
        self.enemies_per_level = 3  # Začíná s méně nepřáteli
        self.boss_active = False
        self.boss = None
        self.level_enemies_killed = 0
        self.enemies_needed_for_boss = 15  # Více nepřátel před bossem - delší level
        self.enemies_spawned = 0  # Celkový počet spawnovaných nepřátel

        # Bonusy
        self.bonuses = []
        self.bonus_spawn_timer = 0

        # Prostředí
        self.environment_offset = 0
        self.clouds = []
        self.waves = []
        self.islands = []
        self.create_environment()

        print("🎮 Spitfire vylepšená verze inicializována!")

    def load_assets(self):
        """Načte grafiku a zvuky"""
        # Inicializace pygame mixer
        pygame.mixer.init()

        # Načtení zvuků
        self.sounds = {}
        sound_files = {
            'shoot': 'assets/sounds/shoot.wav',
            'explosion': 'assets/sounds/explosion.wav',
            'hit': 'assets/sounds/hit.wav',
            'powerup': 'assets/sounds/powerup.wav'
        }

        for name, file in sound_files.items():
            try:
                sound = pygame.mixer.Sound(file)
                sound.set_volume(self.sfx_volume)  # Nastavení hlasitosti
                self.sounds[name] = sound
                print(f"✅ Načten zvuk: {name}")
            except:
                # Vytvoření tichého zvuku jako fallback
                self.sounds[name] = pygame.mixer.Sound(buffer=bytearray([]))
                print(f"⚠️ Zvuk {name} nenalezen, používám tichý")

        # Načtení hudby
        try:
            pygame.mixer.music.load("assets/music/battle_music.wav")
            pygame.mixer.music.set_volume(self.music_volume)
            pygame.mixer.music.play(-1)
            print("🎵 Hudba spuštěna")
        except:
            print("⚠️ Hudba nenalezena")

        # Načtení grafiky
        self.images = {}
        try:
            # Pokus o načtení existujících obrázků
            self.images['player'] = pygame.image.load("assets/images/spitfire.png")
            print("✅ Načtena grafika hráče z souboru")
        except:
            # Vytvoření grafiky programově
            pass

        # Vždy vytvoř grafiku programově (fallback)
        self.create_graphics()

    def create_graphics(self):
        """Vytvoří grafiku programově"""
        print("🎨 Vytvářím grafiku programově...")

        # Hráčovo letadlo - Spitfire (pouze pokud nebylo načteno ze souboru)
        if 'player' not in self.images:
            player_surf = pygame.Surface((40, 30), pygame.SRCALPHA)
            # Trup
            pygame.draw.ellipse(player_surf, (100, 100, 100), (15, 5, 10, 20))
            # Křídla
            pygame.draw.ellipse(player_surf, (80, 80, 80), (0, 12, 40, 6))
            # Kokpit
            pygame.draw.circle(player_surf, (50, 50, 150), (20, 10), 3)
            # Vrtule
            pygame.draw.line(player_surf, (200, 200, 200), (20, 0), (20, 5), 2)
            self.images['player'] = player_surf
            print("✅ Vytvořena grafika hráče")

        # Nepřátelská letadla - vždy vytvoř
        self.create_enemy_graphics()

        # Grafika bossů
        self.create_boss_graphics()

        # Grafika bonusů
        self.create_bonus_graphics()

    def create_enemy_graphics(self):
        """Vytvoří krásnou grafiku nepřátel"""
        # Fighter - německý Messerschmitt Bf 109
        fighter_surf = pygame.Surface((32, 28), pygame.SRCALPHA)
        # Trup
        pygame.draw.ellipse(fighter_surf, (120, 120, 120), (12, 8, 8, 16))
        # Křídla
        pygame.draw.ellipse(fighter_surf, (100, 100, 100), (2, 12, 28, 6))
        # Kokpit
        pygame.draw.circle(fighter_surf, (50, 50, 50), (16, 12), 3)
        # Vrtule
        pygame.draw.circle(fighter_surf, (80, 80, 80), (16, 6), 2)
        # Německý kříž
        pygame.draw.line(fighter_surf, (0, 0, 0), (8, 14), (12, 14), 2)
        pygame.draw.line(fighter_surf, (0, 0, 0), (10, 12), (10, 16), 2)
        pygame.draw.line(fighter_surf, (0, 0, 0), (20, 14), (24, 14), 2)
        pygame.draw.line(fighter_surf, (0, 0, 0), (22, 12), (22, 16), 2)
        self.images['fighter'] = fighter_surf

        # Bomber - německý Heinkel He 111
        bomber_surf = pygame.Surface((55, 40), pygame.SRCALPHA)
        # Hlavní trup
        pygame.draw.ellipse(bomber_surf, (80, 80, 120), (20, 12, 15, 20))
        # Křídla
        pygame.draw.ellipse(bomber_surf, (70, 70, 110), (0, 18, 55, 8))
        # Motory na křídlech
        pygame.draw.circle(bomber_surf, (60, 60, 100), (12, 22), 4)
        pygame.draw.circle(bomber_surf, (60, 60, 100), (43, 22), 4)
        # Kokpit
        pygame.draw.circle(bomber_surf, (40, 40, 80), (27, 8), 4)
        # Ocasní část
        pygame.draw.polygon(bomber_surf, (70, 70, 110), [(27, 32), (22, 38), (32, 38)])
        # Německé označení
        pygame.draw.circle(bomber_surf, (0, 0, 0), (27, 22), 3, 2)
        self.images['bomber'] = bomber_surf

        # Interceptor - německý Focke-Wulf Fw 190
        interceptor_surf = pygame.Surface((28, 24), pygame.SRCALPHA)
        # Trup - kratší a širší
        pygame.draw.ellipse(interceptor_surf, (140, 140, 60), (10, 6, 8, 14))
        # Křídla - kratší pro rychlost
        pygame.draw.ellipse(interceptor_surf, (120, 120, 50), (3, 10, 22, 5))
        # Kokpit
        pygame.draw.circle(interceptor_surf, (80, 80, 30), (14, 10), 2)
        # Vrtule
        pygame.draw.circle(interceptor_surf, (100, 100, 40), (14, 4), 2)
        # Žluté pruhy (rychlostní označení)
        pygame.draw.line(interceptor_surf, (255, 255, 0), (6, 12), (10, 12), 1)
        pygame.draw.line(interceptor_surf, (255, 255, 0), (18, 12), (22, 12), 1)
        self.images['interceptor'] = interceptor_surf

        # Ace - červený Baron (Fokker Dr.I)
        ace_surf = pygame.Surface((36, 32), pygame.SRCALPHA)
        # Trup
        pygame.draw.ellipse(ace_surf, (180, 50, 50), (14, 10, 8, 16))
        # Horní křídla
        pygame.draw.ellipse(ace_surf, (160, 40, 40), (4, 8, 28, 6))
        # Dolní křídla
        pygame.draw.ellipse(ace_surf, (160, 40, 40), (6, 16, 24, 5))
        # Kokpit
        pygame.draw.circle(ace_surf, (100, 20, 20), (18, 12), 3)
        # Vrtule
        pygame.draw.circle(ace_surf, (120, 30, 30), (18, 6), 2)
        # Bílý kříž (německé označení)
        pygame.draw.line(ace_surf, (255, 255, 255), (10, 10), (14, 10), 2)
        pygame.draw.line(ace_surf, (255, 255, 255), (12, 8), (12, 12), 2)
        pygame.draw.line(ace_surf, (255, 255, 255), (22, 10), (26, 10), 2)
        pygame.draw.line(ace_surf, (255, 255, 255), (24, 8), (24, 12), 2)
        # Červené detaily
        pygame.draw.circle(ace_surf, (255, 0, 0), (18, 18), 2)
        self.images['ace'] = ace_surf

        print("✅ Krásná grafika nepřátel vytvořena")
        print(f"📊 Dostupné obrázky: {list(self.images.keys())}")

    def run(self):
        """Hlavní herní smyčka"""
        while self.running:
            frame_start = pygame.time.get_ticks()

            # Zpracování událostí
            self.handle_events()

            # Aktualizace podle stavu
            if self.current_state == GameState.MENU:
                self.update_menu()
            elif self.current_state == GameState.PLAYING:
                self.update_game()
            elif self.current_state == GameState.SETTINGS:
                self.update_settings()

            # Vykreslení
            self.render()

            # Statistiky výkonu
            frame_time = pygame.time.get_ticks() - frame_start
            self.fps_history.append(self.clock.get_fps())
            if len(self.fps_history) > 60:
                self.fps_history.pop(0)

            # Udržení FPS
            self.clock.tick(FPS)
            self.frame_count += 1

        self.cleanup()

    def handle_events(self):
        """Zpracování událostí"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False

            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    if self.current_state == GameState.PLAYING:
                        self.current_state = GameState.MENU
                    else:
                        self.running = False

                elif event.key == pygame.K_F1:
                    self.show_help()

                elif event.key == pygame.K_F11:
                    self.toggle_fullscreen()

                # Menu navigace
                elif self.current_state == GameState.MENU:
                    if event.key == pygame.K_UP:
                        self.menu_selection = (self.menu_selection - 1) % len(self.menu_options)
                    elif event.key == pygame.K_DOWN:
                        self.menu_selection = (self.menu_selection + 1) % len(self.menu_options)
                    elif event.key == pygame.K_RETURN:
                        self.select_menu_option()

    def select_menu_option(self):
        """Vybere možnost z menu"""
        if self.menu_selection == 0:  # Nová hra
            self.current_state = GameState.PLAYING
            self.start_new_game()
        elif self.menu_selection == 1:  # Nastavení
            self.current_state = GameState.SETTINGS
        elif self.menu_selection == 2:  # Konec
            self.running = False

    def start_new_game(self):
        """Spustí novou hru"""
        self.player_pos = [SCREEN_WIDTH//2, SCREEN_HEIGHT//2]
        self.player_health = 100
        self.enemies = []
        self.bullets = []
        self.enemy_bullets = []
        self.score = 0
        self.level = 1
        self.enemy_spawn_timer = 0
        self.formation_timer = 0
        print("🚀 Nová hra spuštěna!")

    def update_menu(self):
        """Aktualizace menu"""
        pass  # Menu je statické

    def update_game(self):
        """Aktualizace hry s pokročilou AI"""
        keys = pygame.key.get_pressed()

        # Pohyb hráče
        if keys[pygame.K_LEFT] and self.player_pos[0] > 20:
            self.player_pos[0] -= 5
        if keys[pygame.K_RIGHT] and self.player_pos[0] < SCREEN_WIDTH - 20:
            self.player_pos[0] += 5
        if keys[pygame.K_UP] and self.player_pos[1] > 20:
            self.player_pos[1] -= 5
        if keys[pygame.K_DOWN] and self.player_pos[1] < SCREEN_HEIGHT - 20:
            self.player_pos[1] += 5

        # Střelba
        if keys[pygame.K_SPACE] and self.frame_count % 10 == 0:
            self.bullets.append([self.player_pos[0], self.player_pos[1] - 20])
            self.sounds['shoot'].play()

        # Aktualizace projektilů hráče
        for bullet in self.bullets[:]:
            bullet[1] -= 10
            if bullet[1] < 0:
                self.bullets.remove(bullet)

        # Aktualizace projektilů nepřátel
        for bullet in self.enemy_bullets[:]:
            bullet[1] += 8
            if bullet[1] > SCREEN_HEIGHT:
                self.enemy_bullets.remove(bullet)

        # Pokročilé vytváření nepřátel
        self.spawn_intelligent_enemies()

        # Aktualizace nepřátel s AI
        self.update_enemies_ai()

        # Aktualizace bosse
        if self.boss_active and self.boss:
            self.update_boss_ai()

        # Aktualizace bonusů
        self.update_bonuses()

        # Aktualizace prostředí
        self.update_environment()

        # Kolize
        self.check_collisions()

    def spawn_intelligent_enemies(self):
        """Pokročilé vytváření nepřátel s postupnou obtížností"""
        self.enemy_spawn_timer += 1
        self.formation_timer += 1

        # Kontrola, zda je čas na bosse - buď zabití nebo spawnovaní
        boss_condition = (self.level_enemies_killed >= self.enemies_needed_for_boss or
                         self.enemies_spawned >= self.enemies_needed_for_boss * 2)

        if boss_condition and not self.boss_active:
            self.spawn_boss()
            return

        # Pokud je boss aktivní, nespawnuj další nepřátele
        if self.boss_active:
            return

        # Dynamická obtížnost podle úrovně
        max_enemies = min(3 + self.level, 8)  # Začíná s 4, max 8
        spawn_delay = max(120 - (self.level * 10), 60)  # Rychlejší spawn s úrovní

        # Základní spawn nepřátel
        if (self.enemy_spawn_timer >= spawn_delay and
            len(self.enemies) < max_enemies):

            enemy_type = self.choose_enemy_type()
            enemy_x = random.randint(50, SCREEN_WIDTH - 50)

            enemy = {
                'type': enemy_type,
                'pos': [enemy_x, -30],
                'health': self.get_enemy_health(enemy_type),
                'max_health': self.get_enemy_health(enemy_type),
                'speed': self.get_enemy_speed(enemy_type),
                'behavior': 'patrol',
                'target_pos': [enemy_x, SCREEN_HEIGHT + 50],
                'shoot_timer': 0,
                'ai_timer': 0,
                'formation_id': None
            }

            self.enemies.append(enemy)
            self.enemy_spawn_timer = 0
            self.enemies_spawned += 1
            print(f"👾 Spawn {enemy_type} ({self.enemies_spawned} celkem, {self.level_enemies_killed} zabito)")

        # Formace nepřátel (pouze na vyšších úrovních)
        formation_delay = max(600 - (self.level * 50), 300)
        if (self.level >= 2 and
            self.formation_timer >= formation_delay and
            len(self.enemies) < max_enemies - 2):
            self.spawn_formation()
            self.formation_timer = 0

    def choose_enemy_type(self):
        """Vybere typ nepřítele podle úrovně"""
        if self.level == 1:
            return random.choice(['fighter', 'fighter', 'interceptor'])
        elif self.level <= 3:
            return random.choice(['fighter', 'interceptor', 'bomber'])
        else:
            return random.choice(['fighter', 'interceptor', 'bomber', 'ace'])

    def get_enemy_health(self, enemy_type):
        """Vrátí zdraví podle typu nepřítele"""
        health_map = {
            'fighter': 30,
            'interceptor': 20,
            'bomber': 80,
            'ace': 50
        }
        return health_map.get(enemy_type, 30)

    def get_enemy_speed(self, enemy_type):
        """Vrátí rychlost podle typu nepřítele"""
        speed_map = {
            'fighter': 2.5,
            'interceptor': 4.0,
            'bomber': 1.5,
            'ace': 3.0
        }
        return speed_map.get(enemy_type, 2.0)

    def spawn_formation(self):
        """Vytvoří formaci nepřátel"""
        formation_id = f"formation_{self.frame_count}"
        formation_center_x = random.randint(100, SCREEN_WIDTH - 100)

        # V-formace
        positions = [
            [0, 0],      # Vůdce
            [-40, 30],   # Levé křídlo
            [40, 30],    # Pravé křídlo
            [-80, 60],   # Levé křídlo 2
            [80, 60]     # Pravé křídlo 2
        ]

        for i, offset in enumerate(positions):
            enemy_type = 'ace' if i == 0 else 'fighter'
            enemy = {
                'type': enemy_type,
                'pos': [formation_center_x + offset[0], -30 + offset[1]],
                'health': self.get_enemy_health(enemy_type),
                'max_health': self.get_enemy_health(enemy_type),
                'speed': self.get_enemy_speed(enemy_type),
                'behavior': 'formation',
                'target_pos': [formation_center_x + offset[0], 150],
                'shoot_timer': 0,
                'ai_timer': 0,
                'formation_id': formation_id,
                'formation_offset': offset,
                'is_leader': i == 0
            }
            self.enemies.append(enemy)

        print(f"🛩️ Formace {formation_id} vytvořena!")

    def spawn_boss(self):
        """Vytvoří bosse podle úrovně"""
        self.boss_active = True
        boss_x = SCREEN_WIDTH // 2

        # Různí bossové podle úrovně
        if self.level == 1:
            boss_type = "giant_bomber"
            boss_health = 200
            boss_name = "Gigantický bombardér"
        elif self.level == 2:
            boss_type = "ace_squadron"
            boss_health = 150
            boss_name = "Eskadra es"
        elif self.level == 3:
            boss_type = "flying_fortress"
            boss_health = 300
            boss_name = "Létající pevnost"
        else:
            boss_type = "super_ace"
            boss_health = 250 + (self.level * 50)
            boss_name = f"Super eso úrovně {self.level}"

        self.boss = {
            'type': boss_type,
            'name': boss_name,
            'pos': [boss_x, -80],
            'health': boss_health,
            'max_health': boss_health,
            'speed': 1.5,
            'behavior': 'boss_enter',
            'target_pos': [boss_x, 180],  # Níže, aby byl vidět přes health bar
            'shoot_timer': 0,
            'ai_timer': 0,
            'phase': 1,
            'special_timer': 0,
            'movement_pattern': 0
        }

        print(f"👑 BOSS SPAWN: {boss_name}!")

    def update_boss_ai(self):
        """Pokročilá AI bosse s různými fázemi"""
        if not self.boss:
            return

        self.boss['ai_timer'] += 1
        self.boss['shoot_timer'] += 1
        self.boss['special_timer'] += 1

        # Aktualizace fáze podle zdraví
        self.update_boss_phase()

        # Pohyb bosse
        if self.boss['behavior'] == 'boss_enter':
            # Boss přilétá
            self.boss['pos'][1] += self.boss['speed']
            if self.boss['pos'][1] >= self.boss['target_pos'][1]:
                self.boss['behavior'] = 'boss_fight'
                print(f"⚔️ {self.boss['name']} vstupuje do boje!")

        elif self.boss['behavior'] == 'boss_fight':
            # Pokročilý pohyb podle typu a fáze
            self.update_boss_movement()

        elif self.boss['behavior'] == 'boss_retreat':
            # Boss se stahuje pro speciální útok
            self.boss['pos'][1] -= 1
            if self.boss['pos'][1] <= 120:
                self.boss['behavior'] = 'boss_fight'

        elif self.boss['behavior'] == 'boss_charge':
            # Boss útočí směrem k hráči
            self.boss_charge_attack()

        # Boss střelba podle fáze
        if self.boss['behavior'] in ['boss_fight', 'boss_charge']:
            self.update_boss_shooting()

        # Speciální útoky podle fáze
        special_delay = self.get_special_attack_delay()
        if self.boss['special_timer'] >= special_delay:
            self.boss_special_attack()
            self.boss['special_timer'] = 0

    def update_boss_phase(self):
        """Aktualizuje fázi bosse podle zdraví"""
        health_ratio = self.boss['health'] / self.boss['max_health']
        old_phase = self.boss['phase']

        if health_ratio > 0.66:
            self.boss['phase'] = 1  # Fáze 1: Základní útok
        elif health_ratio > 0.33:
            self.boss['phase'] = 2  # Fáze 2: Agresivnější
        else:
            self.boss['phase'] = 3  # Fáze 3: Zoufalý útok

        # Oznámení změny fáze
        if old_phase != self.boss['phase']:
            phase_names = {1: "Základní útok", 2: "Agresivní fáze", 3: "Zoufalý útok"}
            print(f"🔥 {self.boss['name']} - {phase_names[self.boss['phase']]}!")

    def update_boss_movement(self):
        """Pokročilý pohyb bosse podle typu a fáze"""
        boss_type = self.boss['type']
        phase = self.boss['phase']

        if boss_type == 'giant_bomber':
            # Bombardér - pohyb přes celou obrazovku s různými vzory
            if phase == 1:
                # Lineární pohyb ze strany na stranu
                self.boss['movement_pattern'] += 1
                if not hasattr(self.boss, 'direction'):
                    self.boss['direction'] = 1

                self.boss['pos'][0] += self.boss['direction'] * 2.0

                # Změna směru na krajích
                if self.boss['pos'][0] <= 60:
                    self.boss['direction'] = 1
                elif self.boss['pos'][0] >= SCREEN_WIDTH - 60:
                    self.boss['direction'] = -1

            elif phase == 2:
                # Sinusový pohyb s větší amplitudou - postupný
                self.boss['movement_pattern'] += 1
                center_x = SCREEN_WIDTH // 2
                amplitude = 150  # Menší amplituda
                target_x = center_x + amplitude * math.sin(self.boss['movement_pattern'] * 0.02)

                # Postupný pohyb k cílové pozici místo teleportace
                dx = target_x - self.boss['pos'][0]
                self.boss['pos'][0] += dx * 0.05  # Pomalý pohyb k cíli

                # Vertikální kývání
                self.boss['pos'][1] += math.sin(self.boss['movement_pattern'] * 0.015) * 0.5

            else:  # phase == 3
                # Rychlejší lineární pohyb ze strany na stranu
                self.boss['movement_pattern'] += 1
                if not hasattr(self.boss, 'direction'):
                    self.boss['direction'] = 1

                # Rychlejší pohyb než ve fázi 1
                self.boss['pos'][0] += self.boss['direction'] * 3.0

                # Změna směru na krajích
                if self.boss['pos'][0] <= 50:
                    self.boss['direction'] = 1
                elif self.boss['pos'][0] >= SCREEN_WIDTH - 50:
                    self.boss['direction'] = -1

                # Mírné vertikální kývání
                self.boss['pos'][1] += math.sin(self.boss['movement_pattern'] * 0.05) * 1.0

        elif boss_type == 'flying_fortress':
            # Létající pevnost - lineární pohyb ze strany na stranu
            if phase == 1:
                # Pomalý lineární pohyb
                self.boss['movement_pattern'] += 1
                if not hasattr(self.boss, 'direction'):
                    self.boss['direction'] = 1

                self.boss['pos'][0] += self.boss['direction'] * 1.5

                # Změna směru na krajích
                if self.boss['pos'][0] <= 70:
                    self.boss['direction'] = 1
                elif self.boss['pos'][0] >= SCREEN_WIDTH - 70:
                    self.boss['direction'] = -1

            elif phase == 2:
                # Rychlejší lineární pohyb
                self.boss['movement_pattern'] += 1
                if not hasattr(self.boss, 'direction'):
                    self.boss['direction'] = 1

                self.boss['pos'][0] += self.boss['direction'] * 2.5

                # Změna směru na krajích
                if self.boss['pos'][0] <= 60:
                    self.boss['direction'] = 1
                elif self.boss['pos'][0] >= SCREEN_WIDTH - 60:
                    self.boss['direction'] = -1

            else:  # phase == 3
                # Nejrychlejší lineární pohyb
                self.boss['movement_pattern'] += 1
                if not hasattr(self.boss, 'direction'):
                    self.boss['direction'] = 1

                self.boss['pos'][0] += self.boss['direction'] * 3.5

                # Změna směru na krajích
                if self.boss['pos'][0] <= 50:
                    self.boss['direction'] = 1
                elif self.boss['pos'][0] >= SCREEN_WIDTH - 50:
                    self.boss['direction'] = -1

        elif boss_type == 'super_ace':
            # Super eso - rychlý lineární pohyb
            if phase == 1:
                # Základní lineární pohyb
                self.boss['movement_pattern'] += 1
                if not hasattr(self.boss, 'direction'):
                    self.boss['direction'] = 1

                self.boss['pos'][0] += self.boss['direction'] * 2.5

                # Změna směru na krajích
                if self.boss['pos'][0] <= 60:
                    self.boss['direction'] = 1
                elif self.boss['pos'][0] >= SCREEN_WIDTH - 60:
                    self.boss['direction'] = -1

            elif phase == 2:
                # Rychlejší lineární pohyb
                self.boss['movement_pattern'] += 1
                if not hasattr(self.boss, 'direction'):
                    self.boss['direction'] = 1

                self.boss['pos'][0] += self.boss['direction'] * 4.0

                # Změna směru na krajích
                if self.boss['pos'][0] <= 50:
                    self.boss['direction'] = 1
                elif self.boss['pos'][0] >= SCREEN_WIDTH - 50:
                    self.boss['direction'] = -1

                # Mírné vertikální kývání
                self.boss['pos'][1] += math.sin(self.boss['movement_pattern'] * 0.03) * 0.8

            else:  # phase == 3
                # Nejrychlejší lineární pohyb
                self.boss['movement_pattern'] += 1
                if not hasattr(self.boss, 'direction'):
                    self.boss['direction'] = 1

                self.boss['pos'][0] += self.boss['direction'] * 5.0

                # Změna směru na krajích
                if self.boss['pos'][0] <= 40:
                    self.boss['direction'] = 1
                elif self.boss['pos'][0] >= SCREEN_WIDTH - 40:
                    self.boss['direction'] = -1

                # Více vertikálního kývání
                self.boss['pos'][1] += math.sin(self.boss['movement_pattern'] * 0.08) * 1.5

        else:  # ace_squadron nebo jiný
            # Lineární pohyb podle fáze
            self.boss['movement_pattern'] += 1
            if not hasattr(self.boss, 'direction'):
                self.boss['direction'] = 1

            # Rychlost podle fáze
            speed = 1.5 + (phase * 0.5)  # 2.0, 2.5, 3.0
            self.boss['pos'][0] += self.boss['direction'] * speed

            # Změna směru na krajích
            margin = 60
            if self.boss['pos'][0] <= margin:
                self.boss['direction'] = 1
            elif self.boss['pos'][0] >= SCREEN_WIDTH - margin:
                self.boss['direction'] = -1

        # Udržení na obrazovce - celá šířka
        if boss_type == 'giant_bomber':
            margin = 40  # Menší margin pro větší boss
        elif boss_type == 'flying_fortress':
            margin = 45  # Větší margin pro flying fortress
        else:
            margin = 25

        # Debug pro eskadru es
        if boss_type == 'ace_squadron':
            old_x = self.boss['pos'][0]

        self.boss['pos'][0] = max(margin, min(SCREEN_WIDTH - margin, self.boss['pos'][0]))
        self.boss['pos'][1] = max(150, min(280, self.boss['pos'][1]))

        # Debug pro eskadru es
        if boss_type == 'ace_squadron' and old_x != self.boss['pos'][0]:
            print(f"🐛 Ace Squadron pozice opravena: {old_x} → {self.boss['pos'][0]}, směr: {self.boss.get('direction', 'N/A')}")

    def boss_charge_attack(self):
        """Boss útočí směrem k hráči"""
        dx = self.player_pos[0] - self.boss['pos'][0]
        dy = self.player_pos[1] - self.boss['pos'][1]
        distance = math.sqrt(dx*dx + dy*dy)

        if distance > 50:
            # Pohyb směrem k hráči
            speed = 3
            self.boss['pos'][0] += (dx / distance) * speed
            self.boss['pos'][1] += (dy / distance) * speed
        else:
            # Dosáhl hráče, vrať se do normálního boje
            self.boss['behavior'] = 'boss_fight'
            self.boss['pos'][1] = 180  # Vrať se na původní pozici

    def update_boss_shooting(self):
        """Vylepšená střelba s kontrolou pozice"""
        phase = self.boss['phase']
        boss_type = self.boss['type']

        # Frekvence střelby podle fáze - pomalejší
        base_frequency = 60  # Pomalejší základní střelba
        shoot_frequency = max(base_frequency - (phase * 15), 30)

        # Základní střelba
        if self.boss['shoot_timer'] >= shoot_frequency:
            # Různé typy útoků podle času - méně často
            attack_type = (self.boss['ai_timer'] // 120) % 3  # Mění se každé 2 sekundy, jen 3 typy

            if attack_type == 0:
                # Normální střelba
                self.boss_shoot()
            elif attack_type == 1:
                # Burst střelba (pouze ve fázi 2+)
                if phase >= 2:
                    self.boss_burst_attack()
                else:
                    self.boss_shoot()
            else:
                # Tracking útok (pouze ve fázi 3)
                if phase >= 3:
                    self.boss_tracking_attack()
                else:
                    self.boss_shoot()

            self.boss['shoot_timer'] = 0

    def boss_burst_attack(self):
        """Rychlá salva projektilů z pozice bosse"""
        if not self.boss:
            return

        boss_x, boss_y = self.boss['pos']
        phase = self.boss['phase']

        # Kontrola pozice
        if boss_x < 0 or boss_x > SCREEN_WIDTH or boss_y < 0 or boss_y > SCREEN_HEIGHT:
            print(f"⚠️ Boss burst z neplatné pozice: ({boss_x}, {boss_y})")
            return

        # Méně projektilů - jen 3-4
        burst_count = 3 if phase == 2 else 4

        for i in range(burst_count):
            offset = (i - burst_count//2) * 20  # Větší rozestup
            # Střílí přímo z pozice bosse
            self.enemy_bullets.append([boss_x + offset, boss_y + 30])

    def boss_tracking_attack(self):
        """Jednoduchý útok sledující hráče"""
        if not self.boss:
            return

        boss_x, boss_y = self.boss['pos']
        player_x, player_y = self.player_pos

        # Kontrola pozice
        if boss_x < 0 or boss_x > SCREEN_WIDTH or boss_y < 0 or boss_y > SCREEN_HEIGHT:
            print(f"⚠️ Boss tracking z neplatné pozice: ({boss_x}, {boss_y})")
            return

        # Jen jeden tracking projektil směřující k hráči
        dx = player_x - boss_x
        dy = player_y - boss_y
        distance = math.sqrt(dx*dx + dy*dy)

        if distance > 0:
            # Normalizace směru
            dx /= distance
            dy /= distance

            # Jeden projektil směřující k hráči z pozice bosse
            start_x = boss_x
            start_y = boss_y + 30
            self.enemy_bullets.append([start_x, start_y])

            # Pouze ve fázi 3 přidej jeden boční projektil
            if self.boss['phase'] >= 3:
                # Jeden boční projektil
                side_offset = 25 if player_x > boss_x else -25
                self.enemy_bullets.append([boss_x + side_offset, boss_y + 30])

    def get_special_attack_delay(self):
        """Vrátí delay pro speciální útoky podle fáze"""
        phase = self.boss['phase']
        base_delay = 200
        return max(base_delay - (phase * 50), 80)

    def boss_shoot(self):
        """Pokročilá boss střelba s různými vzory"""
        if not self.boss:
            return

        boss_x, boss_y = self.boss['pos']
        boss_type = self.boss['type']
        phase = self.boss['phase']

        # Debug - kontrola pozice bosse
        if boss_x < 0 or boss_x > SCREEN_WIDTH or boss_y < 0 or boss_y > SCREEN_HEIGHT:
            print(f"⚠️ Boss střílí z neplatné pozice: ({boss_x}, {boss_y})")
            return

        # Různé vzory střelby podle typu a fáze
        if boss_type == 'giant_bomber':
            if phase == 1:
                # Základní trojitá střelba
                for offset in [-30, 0, 30]:
                    self.enemy_bullets.append([boss_x + offset, boss_y + 30])
            elif phase == 2:
                # Čtyřitá střelba
                for offset in [-30, -10, 10, 30]:
                    self.enemy_bullets.append([boss_x + offset, boss_y + 30])
            else:  # phase == 3
                # Pětitá střelba + jedna predikce
                for offset in [-40, -20, 0, 20, 40]:
                    self.enemy_bullets.append([boss_x + offset, boss_y + 30])
                # Jedna predikce pozice hráče
                pred_x = self.player_pos[0]
                self.enemy_bullets.append([pred_x, boss_y + 40])

        elif boss_type == 'flying_fortress':
            if phase == 1:
                # Základní trojitá střelba
                for offset in [-25, 0, 25]:
                    self.enemy_bullets.append([boss_x + offset, boss_y + 35])
            elif phase == 2:
                # Čtyřitá střelba
                for offset in [-30, -10, 10, 30]:
                    self.enemy_bullets.append([boss_x + offset, boss_y + 35])
            else:  # phase == 3
                # Pětitá střelba + jedna směrová
                for offset in [-40, -20, 0, 20, 40]:
                    self.enemy_bullets.append([boss_x + offset, boss_y + 35])
                # Jedna směrová střela k hráči
                dx = self.player_pos[0] - boss_x
                if abs(dx) > 10:  # Pouze pokud hráč není přímo pod bossem
                    direction = 20 if dx > 0 else -20
                    self.enemy_bullets.append([boss_x + direction, boss_y + 35])

        elif boss_type == 'super_ace':
            if phase == 1:
                # Rychlá trojitá střelba
                for offset in [-20, 0, 20]:
                    self.enemy_bullets.append([boss_x + offset, boss_y + 25])
            elif phase == 2:
                # Čtyřitá střelba
                for offset in [-25, -8, 8, 25]:
                    self.enemy_bullets.append([boss_x + offset, boss_y + 25])
            else:  # phase == 3
                # Pětitá střelba
                for offset in [-30, -15, 0, 15, 30]:
                    self.enemy_bullets.append([boss_x + offset, boss_y + 25])

        else:  # ace_squadron
            if phase == 1:
                # Formační střelba
                for offset in [-25, 0, 25]:
                    self.enemy_bullets.append([boss_x + offset, boss_y + 30])
            elif phase == 2:
                # Rozšířená formace
                for offset in [-30, -10, 10, 30]:
                    self.enemy_bullets.append([boss_x + offset, boss_y + 30])
            else:  # phase == 3
                # Pětitá formace
                for offset in [-35, -18, 0, 18, 35]:
                    self.enemy_bullets.append([boss_x + offset, boss_y + 30])

    def boss_special_attack(self):
        """Pokročilé speciální útoky podle typu a fáze"""
        if not self.boss:
            return

        boss_x, boss_y = self.boss['pos']
        boss_type = self.boss['type']
        phase = self.boss['phase']

        if boss_type == 'giant_bomber':
            if phase == 1:
                # Základní bombardování
                for i in range(3):
                    x = boss_x - 30 + i * 30
                    self.enemy_bullets.append([x, boss_y + 40])
            elif phase == 2:
                # Hustší bombardování
                for i in range(5):
                    x = boss_x - 40 + i * 20
                    self.enemy_bullets.append([x, boss_y + 40])
                # Dodatečné boční střely
                self.enemy_bullets.append([boss_x - 60, boss_y + 20])
                self.enemy_bullets.append([boss_x + 60, boss_y + 20])
            else:  # phase == 3
                # Masivní bombardování + predikce hráče
                for i in range(7):
                    x = boss_x - 60 + i * 20
                    self.enemy_bullets.append([x, boss_y + 40])
                # Predikce pozice hráče
                for i in range(3):
                    x = self.player_pos[0] - 20 + i * 20
                    self.enemy_bullets.append([x, boss_y + 50])

        elif boss_type == 'flying_fortress':
            if phase == 1:
                # Základní kruhová salva
                for angle in range(0, 360, 60):
                    rad = math.radians(angle)
                    x = boss_x + 25 * math.cos(rad)
                    y = boss_y + 25 * math.sin(rad)
                    self.enemy_bullets.append([x, y])
            elif phase == 2:
                # Hustší kruhová salva + směrové střely
                for angle in range(0, 360, 45):
                    rad = math.radians(angle)
                    x = boss_x + 30 * math.cos(rad)
                    y = boss_y + 30 * math.sin(rad)
                    self.enemy_bullets.append([x, y])
                # Směrové střely k hráči
                dx = self.player_pos[0] - boss_x
                dy = self.player_pos[1] - boss_y
                distance = math.sqrt(dx*dx + dy*dy)
                if distance > 0:
                    for i in range(3):
                        offset = (i - 1) * 15
                        x = boss_x + (dx / distance) * 30 + offset
                        y = boss_y + (dy / distance) * 30
                        self.enemy_bullets.append([x, y])
            else:  # phase == 3
                # Masivní kruhová salva + laser beam simulace
                for angle in range(0, 360, 30):
                    rad = math.radians(angle)
                    for radius in [25, 40, 55]:
                        x = boss_x + radius * math.cos(rad)
                        y = boss_y + radius * math.sin(rad)
                        self.enemy_bullets.append([x, y])

        elif boss_type == 'super_ace':
            if phase == 1:
                # Rychlé střely ve formaci
                for i in range(3):
                    x = boss_x - 15 + i * 15
                    self.enemy_bullets.append([x, boss_y + 25])
            elif phase == 2:
                # Zigzag pattern
                for i in range(5):
                    x = boss_x - 30 + i * 15
                    y = boss_y + 25 + (i % 2) * 10
                    self.enemy_bullets.append([x, y])
            else:  # phase == 3
                # Kamikaze pattern - střely všemi směry
                for angle in range(0, 360, 30):
                    rad = math.radians(angle)
                    x = boss_x + 20 * math.cos(rad)
                    y = boss_y + 20 * math.sin(rad)
                    self.enemy_bullets.append([x, y])
                # Speciální charge útok
                if self.boss['ai_timer'] % 200 < 10:
                    self.boss['behavior'] = 'boss_charge'

        else:  # ace_squadron
            # Eskadra - koordinované útoky
            if phase == 1:
                # Základní formační střelba
                for i in range(3):
                    x = boss_x - 20 + i * 20
                    self.enemy_bullets.append([x, boss_y + 30])
            elif phase == 2:
                # Rozšířená formace
                for i in range(5):
                    x = boss_x - 40 + i * 20
                    y = boss_y + 30 + (i % 2) * 5
                    self.enemy_bullets.append([x, y])
            else:  # phase == 3
                # Chaotické útoky ze všech stran
                for i in range(7):
                    x = boss_x - 60 + i * 20
                    y = boss_y + 30 + random.randint(-10, 10)
                    self.enemy_bullets.append([x, y])

        # Oznámení speciálního útoku
        phase_names = {1: "základní", 2: "pokročilý", 3: "zoufalý"}
        print(f"💥 {self.boss['name']} - {phase_names[phase]} speciální útok!")

    def create_boss_graphics(self):
        """Vytvoří grafiku bossů"""
        # Giant Bomber
        giant_bomber_surf = pygame.Surface((80, 60), pygame.SRCALPHA)
        pygame.draw.ellipse(giant_bomber_surf, (60, 60, 100), (25, 20, 30, 25))
        pygame.draw.ellipse(giant_bomber_surf, (50, 50, 90), (0, 28, 80, 12))
        # 4 motory
        for i in range(4):
            x = 10 + i * 20
            pygame.draw.circle(giant_bomber_surf, (40, 40, 80), (x, 34), 6)
        # Kokpit
        pygame.draw.circle(giant_bomber_surf, (30, 30, 70), (40, 15), 6)
        self.images['giant_bomber'] = giant_bomber_surf

        # Flying Fortress
        fortress_surf = pygame.Surface((90, 70), pygame.SRCALPHA)
        pygame.draw.ellipse(fortress_surf, (80, 80, 80), (30, 25, 30, 30))
        pygame.draw.ellipse(fortress_surf, (70, 70, 70), (0, 32, 90, 15))
        # Věže s kanóny
        pygame.draw.circle(fortress_surf, (60, 60, 60), (20, 25), 5)
        pygame.draw.circle(fortress_surf, (60, 60, 60), (70, 25), 5)
        pygame.draw.circle(fortress_surf, (60, 60, 60), (45, 50), 5)
        self.images['flying_fortress'] = fortress_surf

        # Super Ace
        super_ace_surf = pygame.Surface((45, 38), pygame.SRCALPHA)
        pygame.draw.ellipse(super_ace_surf, (255, 0, 0), (18, 12, 10, 20))
        pygame.draw.ellipse(super_ace_surf, (200, 0, 0), (5, 16, 35, 8))
        # Zlaté detaily
        pygame.draw.circle(super_ace_surf, (255, 215, 0), (22, 8), 4)
        pygame.draw.circle(super_ace_surf, (255, 215, 0), (22, 25), 3)
        self.images['super_ace'] = super_ace_surf

    def create_bonus_graphics(self):
        """Vytvoří krásnou grafiku bonusů"""
        print("🎁 Vytvářím grafiku bonusů...")

        # Health bonus - červený kříž
        health_surf = pygame.Surface((24, 24), pygame.SRCALPHA)
        # Červený kříž
        pygame.draw.rect(health_surf, (255, 50, 50), (8, 4, 8, 16))  # Vertikální
        pygame.draw.rect(health_surf, (255, 50, 50), (4, 8, 16, 8))  # Horizontální
        # Bílý okraj
        pygame.draw.rect(health_surf, (255, 255, 255), (8, 4, 8, 16), 2)
        pygame.draw.rect(health_surf, (255, 255, 255), (4, 8, 16, 8), 2)
        # Světelný efekt
        pygame.draw.rect(health_surf, (255, 150, 150), (9, 5, 6, 14))
        pygame.draw.rect(health_surf, (255, 150, 150), (5, 9, 14, 6))
        self.images['bonus_health'] = health_surf

        # Score bonus - zlatá hvězda
        score_surf = pygame.Surface((24, 24), pygame.SRCALPHA)
        # Hvězda body
        star_points = [
            (12, 2),   # Vrchol
            (14, 8),   # Pravý vnitřní
            (20, 8),   # Pravý vnější
            (16, 12),  # Pravý dolní vnitřní
            (18, 18),  # Pravý dolní vnější
            (12, 15),  # Dolní vnitřní
            (6, 18),   # Levý dolní vnější
            (8, 12),   # Levý dolní vnitřní
            (4, 8),    # Levý vnější
            (10, 8)    # Levý vnitřní
        ]
        pygame.draw.polygon(score_surf, (255, 215, 0), star_points)  # Zlatá
        pygame.draw.polygon(score_surf, (255, 255, 100), star_points, 2)  # Světlý okraj
        # Vnitřní lesk
        inner_points = [(12, 4), (13, 8), (16, 8), (14, 11), (15, 15), (12, 13), (9, 15), (10, 11), (8, 8), (11, 8)]
        pygame.draw.polygon(score_surf, (255, 255, 150), inner_points)
        self.images['bonus_score'] = score_surf

        # Weapon bonus - zelená střela
        weapon_surf = pygame.Surface((24, 24), pygame.SRCALPHA)
        # Střela
        pygame.draw.polygon(weapon_surf, (50, 255, 50), [(12, 2), (8, 10), (10, 10), (10, 20), (14, 20), (14, 10), (16, 10)])
        # Okraj
        pygame.draw.polygon(weapon_surf, (255, 255, 255), [(12, 2), (8, 10), (10, 10), (10, 20), (14, 20), (14, 10), (16, 10)], 2)
        # Lesk
        pygame.draw.polygon(weapon_surf, (150, 255, 150), [(12, 3), (9, 9), (10, 9), (10, 19), (13, 19), (13, 9), (15, 9)])
        self.images['bonus_weapon'] = weapon_surf

        # Shield bonus - modrý štít
        shield_surf = pygame.Surface((24, 24), pygame.SRCALPHA)
        # Štít tvar
        shield_points = [(12, 2), (18, 6), (18, 14), (12, 22), (6, 14), (6, 6)]
        pygame.draw.polygon(shield_surf, (50, 50, 255), shield_points)
        # Okraj
        pygame.draw.polygon(shield_surf, (255, 255, 255), shield_points, 2)
        # Vnitřní lesk
        inner_shield = [(12, 4), (16, 7), (16, 13), (12, 19), (8, 13), (8, 7)]
        pygame.draw.polygon(shield_surf, (150, 150, 255), inner_shield)
        # Kříž na štítu
        pygame.draw.line(shield_surf, (255, 255, 255), (12, 6), (12, 18), 2)
        pygame.draw.line(shield_surf, (255, 255, 255), (8, 12), (16, 12), 2)
        self.images['bonus_shield'] = shield_surf

        print("✅ Grafika bonusů vytvořena")

    def create_environment(self):
        """Vytvoří prostředí - moře, mraky, ostrovy"""
        print("🌊 Vytvářím prostředí...")

        # Vytvoření mraků
        for i in range(8):
            cloud = {
                'x': random.randint(-100, SCREEN_WIDTH + 100),
                'y': random.randint(50, 200),
                'size': random.randint(30, 80),
                'speed': random.uniform(0.2, 0.8),
                'opacity': random.randint(100, 180)
            }
            self.clouds.append(cloud)

        # Vytvoření vln
        for i in range(20):
            wave = {
                'x': i * (SCREEN_WIDTH // 20),
                'amplitude': random.randint(5, 15),
                'frequency': random.uniform(0.02, 0.05),
                'phase': random.uniform(0, math.pi * 2)
            }
            self.waves.append(wave)

        # Vytvoření ostrovů
        for i in range(3):
            island = {
                'x': random.randint(100, SCREEN_WIDTH - 100),
                'y': random.randint(SCREEN_HEIGHT - 150, SCREEN_HEIGHT - 50),
                'size': random.randint(40, 100),
                'type': random.choice(['small', 'medium', 'large'])
            }
            self.islands.append(island)

        print("✅ Prostředí vytvořeno")

    def update_environment(self):
        """Aktualizuje animované prostředí"""
        self.environment_offset += 1

        # Pohyb mraků
        for cloud in self.clouds:
            cloud['x'] -= cloud['speed']
            # Reset mraku když vyjde z obrazovky
            if cloud['x'] < -100:
                cloud['x'] = SCREEN_WIDTH + 100
                cloud['y'] = random.randint(50, 200)

        # Animace vln
        for wave in self.waves:
            wave['phase'] += wave['frequency']

        # Pohyb ostrovů (pomalý)
        for island in self.islands:
            island['y'] += 0.3
            # Reset ostrova když vyjde z obrazovky
            if island['y'] > SCREEN_HEIGHT + 50:
                island['y'] = -100
                island['x'] = random.randint(100, SCREEN_WIDTH - 100)

    def update_enemies_ai(self):
        """Aktualizuje AI všech nepřátel"""
        for enemy in self.enemies[:]:
            enemy['ai_timer'] += 1
            enemy['shoot_timer'] += 1

            # AI rozhodování každých 30 framů
            if enemy['ai_timer'] >= 30:
                self.update_enemy_behavior(enemy)
                enemy['ai_timer'] = 0

            # Pohyb podle chování
            self.move_enemy(enemy)

            # Střelba
            self.enemy_shooting(enemy)

            # Odstranění nepřátel mimo obrazovku - včetně těch co zůstali dole
            if (enemy['pos'][1] > SCREEN_HEIGHT + 20 or
                enemy['pos'][1] < -50 or
                enemy['pos'][0] < -50 or enemy['pos'][0] > SCREEN_WIDTH + 50):
                self.enemies.remove(enemy)
                # Počítej jako prolétnutého nepřítele pro boss spawn
                if not self.boss_active:
                    self.level_enemies_killed += 1
                    print(f"👻 Nepřítel prolétl ({self.level_enemies_killed}/{self.enemies_needed_for_boss})")

    def update_enemy_behavior(self, enemy):
        """Aktualizuje chování nepřítele"""
        player_distance = math.sqrt(
            (enemy['pos'][0] - self.player_pos[0])**2 +
            (enemy['pos'][1] - self.player_pos[1])**2
        )

        # Rozhodování podle typu a vzdálenosti
        if enemy['type'] == 'interceptor' and player_distance < 150:
            enemy['behavior'] = 'chase'
            enemy['target_pos'] = self.player_pos[:]

        elif enemy['type'] == 'bomber':
            # Bombardér letí rovně dolů
            enemy['behavior'] = 'patrol'
            enemy['target_pos'] = [enemy['pos'][0], SCREEN_HEIGHT + 50]

        elif enemy['type'] == 'ace':
            if player_distance < 200:
                enemy['behavior'] = 'attack'
                # Kruhový pohyb kolem hráče
                angle = math.atan2(enemy['pos'][1] - self.player_pos[1],
                                 enemy['pos'][0] - self.player_pos[0])
                enemy['target_pos'] = [
                    self.player_pos[0] + 100 * math.cos(angle + 0.5),
                    self.player_pos[1] + 100 * math.sin(angle + 0.5)
                ]
            else:
                enemy['behavior'] = 'patrol'

        elif enemy['behavior'] == 'formation':
            # Formační let - následuje vůdce
            if enemy.get('formation_id') and not enemy.get('is_leader', False):
                leader = self.find_formation_leader(enemy['formation_id'])
                if leader:
                    enemy['target_pos'] = [
                        leader['pos'][0] + enemy['formation_offset'][0],
                        leader['pos'][1] + enemy['formation_offset'][1]
                    ]

        else:
            # Základní patrol - pokračuj dolů
            enemy['behavior'] = 'patrol'
            enemy['target_pos'] = [enemy['pos'][0], SCREEN_HEIGHT + 50]

    def find_formation_leader(self, formation_id):
        """Najde vůdce formace"""
        for enemy in self.enemies:
            if (enemy.get('formation_id') == formation_id and
                enemy.get('is_leader', False)):
                return enemy
        return None

    def move_enemy(self, enemy):
        """Pohybuje nepřítelem podle jeho chování"""
        target_x, target_y = enemy['target_pos']
        current_x, current_y = enemy['pos']

        # Výpočet směru
        dx = target_x - current_x
        dy = target_y - current_y
        distance = math.sqrt(dx*dx + dy*dy)

        speed = enemy['speed']

        if enemy['behavior'] == 'patrol':
            # Patrol - jednoduše letí dolů
            enemy['pos'][1] += speed
            # Mírné kývání ze strany na stranu
            enemy['pos'][0] += math.sin(self.frame_count * 0.02) * 0.5

        elif enemy['behavior'] == 'chase':
            # Chase - pronásleduje hráče
            if distance > 0:
                dx /= distance
                dy /= distance
                enemy['pos'][0] += dx * speed * 1.2
                enemy['pos'][1] += dy * speed * 1.2

        elif enemy['behavior'] == 'attack':
            # Attack - kruhový pohyb kolem hráče
            if distance > 0:
                dx /= distance
                dy /= distance
                enemy['pos'][0] += dx * speed
                enemy['pos'][1] += dy * speed

        elif enemy['behavior'] == 'formation':
            # Formation - drží pozici ve formaci
            if distance > 10:  # Větší tolerance pro formaci
                dx /= distance
                dy /= distance
                enemy['pos'][0] += dx * speed * 0.8
                enemy['pos'][1] += dy * speed * 0.8
            else:
                # Pokud je ve formaci, pokračuj dolů s vůdcem
                enemy['pos'][1] += speed * 0.5

        else:
            # Výchozí chování - letí dolů
            enemy['pos'][1] += speed

    def enemy_shooting(self, enemy):
        """Logika střelby nepřátel"""
        # Různé typy střílí s různou frekvencí
        shoot_frequency = {
            'fighter': 120,      # Každé 2 sekundy
            'interceptor': 90,   # Každé 1.5 sekundy
            'bomber': 180,       # Každé 3 sekundy
            'ace': 60            # Každou sekundu
        }

        freq = shoot_frequency.get(enemy['type'], 120)

        if enemy['shoot_timer'] >= freq:
            # Kontrola, zda je hráč v dosahu
            player_distance = math.sqrt(
                (enemy['pos'][0] - self.player_pos[0])**2 +
                (enemy['pos'][1] - self.player_pos[1])**2
            )

            if player_distance < 300 and enemy['pos'][1] > 0:
                # Predikce pozice hráče pro lepší přesnost
                target_x = self.player_pos[0]
                target_y = self.player_pos[1]

                # Ace má lepší přesnost
                if enemy['type'] == 'ace':
                    # Žádná nepřesnost
                    pass
                else:
                    # Přidání nepřesnosti
                    inaccuracy = 30 if enemy['type'] == 'fighter' else 50
                    target_x += random.randint(-inaccuracy, inaccuracy)
                    target_y += random.randint(-inaccuracy, inaccuracy)

                # Vytvoření projektilu
                self.enemy_bullets.append([enemy['pos'][0], enemy['pos'][1] + 15])
                enemy['shoot_timer'] = 0

    def check_collisions(self):
        """Kontroluje všechny kolize"""
        # Kolize střel hráče s nepřáteli
        for bullet in self.bullets[:]:
            for enemy in self.enemies[:]:
                if (abs(bullet[0] - enemy['pos'][0]) < 25 and
                    abs(bullet[1] - enemy['pos'][1]) < 25):

                    # Zásah nepřítele
                    self.bullets.remove(bullet)
                    enemy['health'] -= 10
                    self.sounds['hit'].play()

                    if enemy['health'] <= 0:
                        # Nepřítel zničen
                        self.enemies.remove(enemy)
                        self.sounds['explosion'].play()

                        # Skóre podle typu
                        score_values = {
                            'fighter': 100,
                            'interceptor': 150,
                            'bomber': 300,
                            'ace': 500
                        }
                        points = score_values.get(enemy['type'], 100)
                        self.score += points
                        self.level_enemies_killed += 1
                        print(f"💥 Zničen {enemy['type']}! +{points} bodů ({self.level_enemies_killed}/{self.enemies_needed_for_boss})")

                    break

        # Kolize střel nepřátel s hráčem
        for bullet in self.enemy_bullets[:]:
            if (abs(bullet[0] - self.player_pos[0]) < 20 and
                abs(bullet[1] - self.player_pos[1]) < 20):

                self.enemy_bullets.remove(bullet)
                self.player_health -= 10
                self.sounds['hit'].play()
                print(f"💔 Hráč zasažen! Zdraví: {self.player_health}")

                if self.player_health <= 0:
                    print("💀 Game Over!")
                    self.current_state = GameState.GAME_OVER

        # Kolize hráče s nepřáteli
        for enemy in self.enemies[:]:
            if (abs(enemy['pos'][0] - self.player_pos[0]) < 30 and
                abs(enemy['pos'][1] - self.player_pos[1]) < 30):

                self.enemies.remove(enemy)
                self.player_health -= 25
                self.sounds['explosion'].play()
                print(f"💥 Kolize! Zdraví: {self.player_health}")

                if self.player_health <= 0:
                    print("💀 Game Over!")
                    self.current_state = GameState.GAME_OVER

        # Kolize střel hráče s bossem
        if self.boss_active and self.boss:
            for bullet in self.bullets[:]:
                if (abs(bullet[0] - self.boss['pos'][0]) < 40 and
                    abs(bullet[1] - self.boss['pos'][1]) < 30):

                    self.bullets.remove(bullet)
                    self.boss['health'] -= 10
                    self.sounds['hit'].play()
                    print(f"🎯 Boss zasažen! Zdraví: {self.boss['health']}/{self.boss['max_health']}")

                    if self.boss['health'] <= 0:
                        # Boss poražen!
                        self.boss_defeated()

                    break

        # Kolize hráče s bonusy
        for bonus in self.bonuses[:]:
            if (abs(bonus['pos'][0] - self.player_pos[0]) < 25 and
                abs(bonus['pos'][1] - self.player_pos[1]) < 25):
                self.collect_bonus(bonus)

    def boss_defeated(self):
        """Boss byl poražen"""
        boss_name = self.boss['name']
        boss_score = 1000 + (self.level * 500)

        self.score += boss_score
        self.sounds['explosion'].play()

        print(f"👑 BOSS PORAŽEN: {boss_name}! +{boss_score} bodů")
        print(f"🎉 Úroveň {self.level} dokončena!")

        # Postup na další úroveň
        self.level += 1
        self.boss_active = False
        self.boss = None
        self.level_enemies_killed = 0
        self.enemies_spawned = 0

        # Reset bonusů pro nový level
        self.bonuses = []
        self.bonus_spawn_timer = 0

        # Zvýšení obtížnosti - delší levely
        self.enemies_needed_for_boss = min(15 + (self.level * 5), 30)

        print(f"🚀 Úroveň {self.level} začíná! Potřeba zabít {self.enemies_needed_for_boss} nepřátel pro bosse.")

    def update_bonuses(self):
        """Aktualizuje bonusy"""
        self.bonus_spawn_timer += 1

        # Spawn bonusů každých 10 sekund
        if self.bonus_spawn_timer >= 600:  # 10 sekund při 60 FPS
            self.spawn_bonus()
            self.bonus_spawn_timer = 0

        # Pohyb bonusů
        for bonus in self.bonuses[:]:
            bonus['pos'][1] += 2  # Pomalý pád dolů

            # Odstranění bonusů mimo obrazovku
            if bonus['pos'][1] > SCREEN_HEIGHT + 50:
                self.bonuses.remove(bonus)

    def spawn_bonus(self):
        """Vytvoří náhodný bonus"""
        bonus_types = ['health', 'score', 'weapon', 'shield']
        bonus_type = random.choice(bonus_types)

        bonus_x = random.randint(50, SCREEN_WIDTH - 50)

        bonus = {
            'type': bonus_type,
            'pos': [bonus_x, -30],
            'timer': 0
        }

        self.bonuses.append(bonus)
        print(f"🎁 Bonus {bonus_type} spawn na pozici {bonus_x}")

    def collect_bonus(self, bonus):
        """Sebere bonus s vizuálními efekty"""
        bonus_type = bonus['type']
        bonus_x, bonus_y = bonus['pos']

        # Vytvoř vizuální efekt sebrání
        self.create_pickup_effect(bonus_x, bonus_y, bonus_type)

        if bonus_type == 'health':
            old_health = self.player_health
            self.player_health = min(100, self.player_health + 25)
            gained = self.player_health - old_health
            if gained > 0:
                print(f"❤️ Zdraví +{gained}! Celkem: {self.player_health}")
            else:
                print(f"❤️ Zdraví již plné! ({self.player_health}/100)")

        elif bonus_type == 'score':
            bonus_score = 500 + (self.level * 100)
            self.score += bonus_score
            print(f"⭐ Skóre +{bonus_score}! Celkem: {self.score}")

        elif bonus_type == 'weapon':
            # Dočasné zlepšení zbraně
            weapon_bonus = 200 + (self.level * 50)
            self.score += weapon_bonus
            print(f"🔫 Zbraň upgrade! +{weapon_bonus} bodů")

        elif bonus_type == 'shield':
            # Dočasný štít
            shield_bonus = 300 + (self.level * 75)
            self.score += shield_bonus
            print(f"🛡️ Štít aktivován! +{shield_bonus} bodů")

        self.sounds['powerup'].play()
        self.bonuses.remove(bonus)

    def create_pickup_effect(self, x, y, bonus_type):
        """Vytvoří vizuální efekt sebrání bonusu"""
        # Jednoduchý efekt - můžeme rozšířit později
        effect_color = self.get_bonus_glow_color(bonus_type)

        # Efekt se přidá do seznamu efektů (implementace později)
        print(f"✨ Efekt sebrání {bonus_type} na pozici ({x}, {y})")

    def update_settings(self):
        """Aktualizace nastavení"""
        keys = pygame.key.get_pressed()

        # Ovládání hlasitosti
        if keys[pygame.K_1]:  # Snížit master volume
            self.master_volume = max(0.0, self.master_volume - 0.05)
            self.update_volumes()
        elif keys[pygame.K_2]:  # Zvýšit master volume
            self.master_volume = min(1.0, self.master_volume + 0.05)
            self.update_volumes()
        elif keys[pygame.K_3]:  # Snížit hudbu
            self.music_volume = max(0.0, self.music_volume - 0.05)
            pygame.mixer.music.set_volume(self.music_volume)
        elif keys[pygame.K_4]:  # Zvýšit hudbu
            self.music_volume = min(1.0, self.music_volume + 0.05)
            pygame.mixer.music.set_volume(self.music_volume)
        elif keys[pygame.K_5]:  # Snížit SFX
            self.sfx_volume = max(0.0, self.sfx_volume - 0.05)
            self.update_volumes()
        elif keys[pygame.K_6]:  # Zvýšit SFX
            self.sfx_volume = min(1.0, self.sfx_volume + 0.05)
            self.update_volumes()

        if keys[pygame.K_ESCAPE]:
            self.current_state = GameState.MENU

    def update_volumes(self):
        """Aktualizuje hlasitost všech zvuků"""
        for sound in self.sounds.values():
            sound.set_volume(self.sfx_volume * self.master_volume)

    def render(self):
        """Vykreslení"""
        self.screen.fill(BLACK)

        if self.current_state == GameState.MENU:
            self.draw_menu()
        elif self.current_state == GameState.PLAYING:
            self.draw_game()
        elif self.current_state == GameState.SETTINGS:
            self.draw_settings()
        elif self.current_state == GameState.GAME_OVER:
            self.draw_game_over()

        # FPS counter
        avg_fps = sum(self.fps_history) / len(self.fps_history) if self.fps_history else 0
        fps_text = pygame.font.Font(None, 24).render(f"FPS: {avg_fps:.1f}", True, YELLOW)
        self.screen.blit(fps_text, (10, 10))

        pygame.display.flip()

    def draw_menu(self):
        """Vykreslí menu"""
        # Titul
        font_large = pygame.font.Font(None, 72)
        title = font_large.render("SPITFIRE", True, WHITE)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, 150))
        self.screen.blit(title, title_rect)

        # Podtitul
        font_medium = pygame.font.Font(None, 36)
        subtitle = font_medium.render("Vylepšená verze", True, YELLOW)
        subtitle_rect = subtitle.get_rect(center=(SCREEN_WIDTH//2, 200))
        self.screen.blit(subtitle, subtitle_rect)

        # Menu možnosti
        font_menu = pygame.font.Font(None, 48)
        for i, option in enumerate(self.menu_options):
            color = YELLOW if i == self.menu_selection else WHITE
            text = font_menu.render(option, True, color)
            text_rect = text.get_rect(center=(SCREEN_WIDTH//2, 300 + i * 60))
            self.screen.blit(text, text_rect)

        # Instrukce
        font_small = pygame.font.Font(None, 24)
        instructions = [
            "↑↓ - Navigace",
            "ENTER - Výběr",
            "ESC - Konec",
            "F1 - Nápověda",
            "F11 - Celá obrazovka"
        ]

        for i, instruction in enumerate(instructions):
            text = font_small.render(instruction, True, WHITE)
            self.screen.blit(text, (50, SCREEN_HEIGHT - 120 + i * 20))

    def draw_environment(self):
        """Vykreslí prostředí z ptačí perspektivy - pohled shora na moře"""
        # Základní moře - tmavě modrá barva
        base_sea_color = (25, 50, 120)
        self.screen.fill(base_sea_color)

        # Vykreslení vln (pohled shora)
        self.draw_waves_topdown()

        # Vykreslení ostrovů (pohled shora)
        self.draw_islands_topdown()

        # Vykreslení stínů mraků na moři
        self.draw_cloud_shadows()

    def draw_waves_topdown(self):
        """Vykreslí vlny z ptačí perspektivy - pohled shora"""
        # Vlny jako světlejší pruhy na moři
        for wave in self.waves:
            # Animovaná pozice vlny
            wave_offset = self.environment_offset * wave['frequency'] + wave['phase']
            wave_y = wave['x'] + wave['amplitude'] * math.sin(wave_offset)

            # Horizontální vlny pohybující se dolů
            wave_y = (wave_y + self.environment_offset * 0.5) % SCREEN_HEIGHT

            # Světlejší modrá pro vlny
            wave_color = (40, 80, 160)

            # Vykreslení vlny jako horizontální pruhu
            for x in range(0, SCREEN_WIDTH, 20):
                wave_intensity = math.sin((x + self.environment_offset) * 0.02) * 0.3 + 0.7
                alpha = int(wave_intensity * 100)

                # Vytvoř surface s průhledností
                wave_surface = pygame.Surface((20, 3), pygame.SRCALPHA)
                wave_surface.fill((*wave_color, alpha))
                self.screen.blit(wave_surface, (x, int(wave_y)))

    def draw_islands_topdown(self):
        """Vykreslí ostrovy z ptačí perspektivy"""
        for island in self.islands:
            # Základní tvar ostrova - kruh
            island_color = (34, 139, 34)  # Tmavě zelená
            beach_color = (194, 178, 128)  # Písková
            shallow_water = (50, 100, 150)  # Mělká voda

            # Mělká voda kolem ostrova
            pygame.draw.circle(self.screen, shallow_water,
                             (int(island['x']), int(island['y'])), island['size'] + 15)

            # Pláž
            pygame.draw.circle(self.screen, beach_color,
                             (int(island['x']), int(island['y'])), island['size'] + 5)

            # Hlavní ostrov
            pygame.draw.circle(self.screen, island_color,
                             (int(island['x']), int(island['y'])), island['size'])

            # Vegetace - tmavší skvrny (fixní pozice, ne náhodné)
            if island['type'] in ['medium', 'large']:
                vegetation_color = (20, 100, 20)
                # Použij ID ostrova pro konzistentní pozice
                island_id = hash((int(island['x']), int(island['y']), island['size'])) % 1000
                random.seed(island_id)  # Fixní seed pro konzistentní pozice

                # Několik menších kruhů pro vegetaci
                for i in range(3):
                    veg_x = island['x'] + random.randint(-island['size']//2, island['size']//2)
                    veg_y = island['y'] + random.randint(-island['size']//2, island['size']//2)
                    veg_size = random.randint(5, 15)
                    pygame.draw.circle(self.screen, vegetation_color,
                                     (int(veg_x), int(veg_y)), veg_size)

                random.seed()  # Reset seed

            # Skaliska na větších ostrovech (fixní pozice)
            if island['type'] == 'large':
                rock_color = (100, 100, 100)
                island_id = hash((int(island['x']), int(island['y']), island['size'])) % 1000
                random.seed(island_id)  # Fixní seed

                for i in range(2):
                    rock_x = island['x'] + random.randint(-island['size']//3, island['size']//3)
                    rock_y = island['y'] + random.randint(-island['size']//3, island['size']//3)
                    rock_size = random.randint(3, 8)
                    pygame.draw.circle(self.screen, rock_color,
                                     (int(rock_x), int(rock_y)), rock_size)

                random.seed()  # Reset seed

    def draw_cloud_shadows(self):
        """Vykreslí stíny mraků na moři"""
        for cloud in self.clouds:
            # Stín mraku na moři - tmavší oblast
            shadow_color = (15, 35, 80)  # Tmavší modrá
            shadow_size = cloud['size'] + 10

            # Stín je mírně posunutý od mraku
            shadow_x = cloud['x'] + 20
            shadow_y = cloud['y'] + 30

            # Pouze pokud je stín na obrazovce
            if 0 <= shadow_x <= SCREEN_WIDTH and 0 <= shadow_y <= SCREEN_HEIGHT:
                # Vytvoř poloprůhledný stín
                shadow_surface = pygame.Surface((shadow_size * 2, shadow_size), pygame.SRCALPHA)
                shadow_alpha = cloud['opacity'] // 3  # Slabší než mrak

                # Eliptický stín
                pygame.draw.ellipse(shadow_surface, (*shadow_color, shadow_alpha),
                                  (0, 0, shadow_size * 2, shadow_size))

                self.screen.blit(shadow_surface, (shadow_x, shadow_y))

    def draw_game(self):
        """Vykreslí hru s krásným prostředím"""
        # Vykreslení prostředí
        self.draw_environment()

        # Hráč - použití grafiky
        player_rect = self.images['player'].get_rect(center=self.player_pos)
        self.screen.blit(self.images['player'], player_rect)

        # Projektily hráče
        for bullet in self.bullets:
            pygame.draw.circle(self.screen, YELLOW, [int(bullet[0]), int(bullet[1])], 3)
            # Světelný efekt
            pygame.draw.circle(self.screen, (255, 255, 150), [int(bullet[0]), int(bullet[1])], 5, 1)

        # Projektily nepřátel
        for bullet in self.enemy_bullets:
            pygame.draw.circle(self.screen, RED, [int(bullet[0]), int(bullet[1])], 4)
            pygame.draw.circle(self.screen, (255, 100, 100), [int(bullet[0]), int(bullet[1])], 6, 1)

        # Nepřátelé s různou grafikou
        for enemy in self.enemies:
            enemy_type = enemy['type']
            enemy_image = self.images.get(enemy_type, self.images['fighter'])
            enemy_rect = enemy_image.get_rect(center=[int(enemy['pos'][0]), int(enemy['pos'][1])])
            self.screen.blit(enemy_image, enemy_rect)

            # Health bar pro nepřátele
            if enemy['health'] < enemy['max_health']:
                self.draw_health_bar(enemy['pos'][0], enemy['pos'][1] - 25,
                                   enemy['health'], enemy['max_health'], 30, 4)

            # Indikátor chování (debug)
            behavior_colors = {
                'patrol': (100, 100, 100),
                'chase': (255, 100, 0),
                'attack': (255, 0, 0),
                'formation': (0, 255, 0)
            }
            behavior_color = behavior_colors.get(enemy['behavior'], (100, 100, 100))
            pygame.draw.circle(self.screen, behavior_color,
                             [int(enemy['pos'][0]), int(enemy['pos'][1]) - 35], 3)

        # Boss
        if self.boss_active and self.boss:
            boss_type = self.boss['type']
            boss_image = self.images.get(boss_type, self.images['fighter'])
            boss_rect = boss_image.get_rect(center=[int(self.boss['pos'][0]), int(self.boss['pos'][1])])
            self.screen.blit(boss_image, boss_rect)

            # Boss health bar
            self.draw_boss_health_bar()

            # Boss name
            font = pygame.font.Font(None, 32)
            name_text = font.render(self.boss['name'], True, (255, 0, 0))
            name_rect = name_text.get_rect(center=(SCREEN_WIDTH//2, 90))  # Výše než health bar
            self.screen.blit(name_text, name_rect)

        # Bonusy
        self.draw_bonuses()

        # HUD
        self.draw_hud()

        # Instrukce
        self.draw_instructions()

    def draw_bonuses(self):
        """Vykreslí bonusy s krásnou grafikou"""
        for bonus in self.bonuses:
            bonus_x, bonus_y = bonus['pos']
            bonus_type = bonus['type']

            # Blikání bonusu - pomalejší pro lepší viditelnost
            blink = (self.frame_count // 15) % 2
            if blink == 0:
                continue

            # Vykreslení bonus grafiky
            bonus_image_key = f'bonus_{bonus_type}'
            if bonus_image_key in self.images:
                bonus_image = self.images[bonus_image_key]
                bonus_rect = bonus_image.get_rect(center=[int(bonus_x), int(bonus_y)])
                self.screen.blit(bonus_image, bonus_rect)

                # Světelný efekt kolem bonusu
                glow_radius = 20 + int(math.sin(self.frame_count * 0.1) * 3)
                glow_color = self.get_bonus_glow_color(bonus_type)
                pygame.draw.circle(self.screen, glow_color, [int(bonus_x), int(bonus_y)], glow_radius, 2)

                # Text typu bonusu pod ikonou
                font = pygame.font.Font(None, 18)
                text = font.render(bonus_type.upper(), True, (255, 255, 255))
                text_rect = text.get_rect(center=(bonus_x, bonus_y + 20))
                # Černý okraj pro lepší čitelnost
                shadow_text = font.render(bonus_type.upper(), True, (0, 0, 0))
                shadow_rect = text_rect.copy()
                shadow_rect.x += 1
                shadow_rect.y += 1
                self.screen.blit(shadow_text, shadow_rect)
                self.screen.blit(text, text_rect)

    def get_bonus_glow_color(self, bonus_type):
        """Vrátí barvu světelného efektu pro bonus"""
        if bonus_type == 'health':
            return (255, 100, 100)  # Červená
        elif bonus_type == 'score':
            return (255, 215, 0)    # Zlatá
        elif bonus_type == 'weapon':
            return (100, 255, 100)  # Zelená
        else:  # shield
            return (100, 150, 255)  # Modrá

    def draw_health_bar(self, x, y, current_health, max_health, width, height):
        """Vykreslí health bar"""
        # Pozadí
        pygame.draw.rect(self.screen, (100, 0, 0), (x - width//2, y, width, height))

        # Zdraví
        health_ratio = current_health / max_health
        health_width = int(width * health_ratio)
        health_color = (255, 0, 0) if health_ratio < 0.3 else (255, 255, 0) if health_ratio < 0.7 else (0, 255, 0)
        pygame.draw.rect(self.screen, health_color, (x - width//2, y, health_width, height))

        # Okraj
        pygame.draw.rect(self.screen, WHITE, (x - width//2, y, width, height), 1)

    def draw_boss_health_bar(self):
        """Vykreslí velký health bar pro bosse"""
        if not self.boss:
            return

        bar_width = 400
        bar_height = 20
        bar_x = SCREEN_WIDTH // 2 - bar_width // 2
        bar_y = 120  # Níže, aby nepřekrýval bosse

        # Pozadí
        pygame.draw.rect(self.screen, (100, 0, 0), (bar_x, bar_y, bar_width, bar_height))

        # Zdraví
        health_ratio = self.boss['health'] / self.boss['max_health']
        health_width = int(bar_width * health_ratio)

        # Barva podle zdraví
        if health_ratio > 0.7:
            health_color = (255, 0, 0)  # Červená
        elif health_ratio > 0.3:
            health_color = (255, 165, 0)  # Oranžová
        else:
            health_color = (255, 255, 0)  # Žlutá (kritické)

        pygame.draw.rect(self.screen, health_color, (bar_x, bar_y, health_width, bar_height))

        # Okraj
        pygame.draw.rect(self.screen, WHITE, (bar_x, bar_y, bar_width, bar_height), 3)

        # Text zdraví
        font = pygame.font.Font(None, 24)
        health_text = font.render(f"{self.boss['health']}/{self.boss['max_health']}", True, WHITE)
        text_rect = health_text.get_rect(center=(SCREEN_WIDTH//2, bar_y + bar_height//2))
        self.screen.blit(health_text, text_rect)

    def draw_hud(self):
        """Vykreslí HUD"""
        font = pygame.font.Font(None, 36)
        font_small = pygame.font.Font(None, 24)

        # Skóre
        score_text = font.render(f"Skóre: {self.score}", True, WHITE)
        self.screen.blit(score_text, (10, 50))

        # Úroveň
        level_text = font.render(f"Úroveň: {self.level}", True, WHITE)
        self.screen.blit(level_text, (10, 90))

        # Zdraví hráče
        health_text = font.render(f"Zdraví: {self.player_health}", True, WHITE)
        self.screen.blit(health_text, (10, 130))

        # Health bar hráče
        self.draw_health_bar(100, 170, self.player_health, 100, 150, 8)

        # Počet nepřátel
        enemy_count = len(self.enemies)
        enemy_text = font_small.render(f"Nepřátelé: {enemy_count}", True, WHITE)
        self.screen.blit(enemy_text, (10, 190))

        # Typy nepřátel
        enemy_types = {}
        for enemy in self.enemies:
            enemy_type = enemy['type']
            enemy_types[enemy_type] = enemy_types.get(enemy_type, 0) + 1

        y_offset = 210
        for enemy_type, count in enemy_types.items():
            type_colors = {
                'fighter': (200, 50, 50),
                'interceptor': (200, 200, 50),
                'bomber': (100, 100, 200),
                'ace': (255, 215, 0)
            }
            color = type_colors.get(enemy_type, WHITE)
            type_text = font_small.render(f"{enemy_type}: {count}", True, color)
            self.screen.blit(type_text, (10, y_offset))
            y_offset += 20

    def draw_instructions(self):
        """Vykreslí instrukce"""
        font_small = pygame.font.Font(None, 24)
        instructions = [
            "Šipky - Pohyb",
            "MEZERNÍK - Střelba",
            "ESC - Menu"
        ]

        for i, instruction in enumerate(instructions):
            text = font_small.render(instruction, True, WHITE)
            self.screen.blit(text, (SCREEN_WIDTH - 150, 50 + i * 20))

    def draw_settings(self):
        """Vykreslí nastavení"""
        font = pygame.font.Font(None, 48)
        title = font.render("NASTAVENÍ", True, WHITE)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, 150))
        self.screen.blit(title, title_rect)

        font_medium = pygame.font.Font(None, 28)
        font_small = pygame.font.Font(None, 24)

        # Aktuální hlasitosti
        volume_text = [
            f"Master Volume: {self.master_volume:.2f}",
            f"Hudba: {self.music_volume:.2f}",
            f"Zvukové efekty: {self.sfx_volume:.2f}",
            "",
            "Ovládání hlasitosti:",
            "1/2 - Master Volume",
            "3/4 - Hudba",
            "5/6 - Zvukové efekty",
            "",
            "ESC - Zpět do menu"
        ]

        for i, text in enumerate(volume_text):
            if text.startswith(("Master", "Hudba", "Zvukové")):
                color = YELLOW
                font = font_medium
            elif text.startswith(("1/2", "3/4", "5/6")):
                color = GREEN
                font = font_small
            elif text == "Ovládání hlasitosti:":
                color = WHITE
                font = font_medium
            else:
                color = WHITE
                font = font_small

            surface = font.render(text, True, color)
            text_rect = surface.get_rect(center=(SCREEN_WIDTH//2, 220 + i * 30))
            self.screen.blit(surface, text_rect)

    def draw_game_over(self):
        """Vykreslí Game Over obrazovku"""
        # Tmavé pozadí
        self.screen.fill((20, 20, 20))

        # Game Over text
        font_large = pygame.font.Font(None, 72)
        game_over_text = font_large.render("GAME OVER", True, RED)
        game_over_rect = game_over_text.get_rect(center=(SCREEN_WIDTH//2, 200))
        self.screen.blit(game_over_text, game_over_rect)

        # Finální skóre
        font_medium = pygame.font.Font(None, 48)
        score_text = font_medium.render(f"Finální skóre: {self.score}", True, WHITE)
        score_rect = score_text.get_rect(center=(SCREEN_WIDTH//2, 280))
        self.screen.blit(score_text, score_rect)

        # Statistiky
        font_small = pygame.font.Font(None, 32)
        stats = [
            f"Úroveň dosažena: {self.level}",
            f"Celkový čas: {(pygame.time.get_ticks() - self.start_time) // 1000}s",
            "",
            "ENTER - Nová hra",
            "ESC - Menu"
        ]

        for i, stat in enumerate(stats):
            color = YELLOW if stat.startswith(("ENTER", "ESC")) else WHITE
            text = font_small.render(stat, True, color)
            text_rect = text.get_rect(center=(SCREEN_WIDTH//2, 350 + i * 40))
            self.screen.blit(text, text_rect)

        # Zpracování kláves
        keys = pygame.key.get_pressed()
        if keys[pygame.K_RETURN]:
            self.start_new_game()
            self.current_state = GameState.PLAYING
        elif keys[pygame.K_ESCAPE]:
            self.current_state = GameState.MENU

    def show_help(self):
        """Zobrazí nápovědu"""
        print("🎮 SPITFIRE - Vylepšená verze")
        print("=" * 40)
        print("Ovládání:")
        print("  Šipky - Pohyb letadla")
        print("  MEZERNÍK - Střelba")
        print("  ESC - Menu/Konec")
        print("  F1 - Tato nápověda")
        print("  F11 - Celá obrazovka")
        print("")
        print("Nové funkce:")
        print("  ✅ Vylepšené menu s navigací")
        print("  ✅ Monitoring výkonu (FPS)")
        print("  ✅ Lepší ovládání")
        print("  ✅ Optimalizovaný kód")
        print("  ✅ Nastavení")

    def toggle_fullscreen(self):
        """Přepne celou obrazovku"""
        pygame.display.toggle_fullscreen()
        print("🖥️ Přepnuto na celou obrazovku")

    def cleanup(self):
        """Úklid při ukončení"""
        print("💾 Ukládám statistiky...")

        # Uložení statistik
        stats = {
            "total_time": pygame.time.get_ticks() - self.start_time,
            "frames_rendered": self.frame_count,
            "final_score": self.score,
            "final_level": self.level
        }

        try:
            with open("game_stats.json", "w") as f:
                json.dump(stats, f, indent=2)
            print("✅ Statistiky uloženy")
        except:
            print("⚠️ Nepodařilo se uložit statistiky")

        pygame.quit()
        print("👋 Spitfire ukončen")

def main():
    """Hlavní funkce"""
    print("🚀 Spouštím Spitfire - Vylepšená verze")

    try:
        game = ImprovedSpitfireGame()
        game.run()
    except Exception as e:
        print(f"❌ Chyba při spuštění hry: {e}")
        import traceback
        traceback.print_exc()

    return 0

if __name__ == "__main__":
    sys.exit(main())
