#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Spitfire Game Asset Downloader
------------------------------
This script downloads and prepares assets for the Spitfire game.
"""

import os
import sys
import urllib.request
import zipfile
import io
import shutil

def create_directories():
    """Create necessary directories for assets."""
    dirs = [
        "assets",
        "assets/images",
        "assets/sounds",
        "assets/music"
    ]
    
    for directory in dirs:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")

def download_file(url, filename):
    """Download a file from URL and save it to the specified filename."""
    try:
        print(f"Downloading {filename} from {url}...")
        urllib.request.urlretrieve(url, filename)
        print(f"Downloaded {filename} successfully!")
        return True
    except Exception as e:
        print(f"Failed to download {filename}: {e}")
        return False

def download_and_extract_zip(url, extract_to):
    """Download a zip file and extract its contents."""
    try:
        print(f"Downloading and extracting zip from {url}...")
        response = urllib.request.urlopen(url)
        zip_data = io.BytesIO(response.read())
        
        with zipfile.ZipFile(zip_data) as zip_file:
            zip_file.extractall(extract_to)
        
        print(f"Extracted zip contents to {extract_to} successfully!")
        return True
    except Exception as e:
        print(f"Failed to download and extract zip: {e}")
        return False

def download_assets():
    """Download all game assets."""
    # Create necessary directories
    create_directories()
    
    # Aircraft sprites from OpenGameArt
    aircraft_zip_url = "https://opengameart.org/sites/default/files/aircraft_sprites.zip"
    download_and_extract_zip(aircraft_zip_url, "temp_aircraft")
    
    # Move the Spitfire image to our assets directory
    if os.path.exists("temp_aircraft"):
        # Find a suitable Spitfire image in the extracted files
        for root, dirs, files in os.walk("temp_aircraft"):
            for file in files:
                if file.lower().endswith(('.png', '.jpg')) and ('spitfire' in file.lower() or 'plane' in file.lower()):
                    src_path = os.path.join(root, file)
                    dst_path = os.path.join("assets/images", "spitfire.png")
                    shutil.copy(src_path, dst_path)
                    print(f"Copied {file} to assets/images/spitfire.png")
                
                # Also look for enemy aircraft images
                if file.lower().endswith(('.png', '.jpg')) and ('enemy' in file.lower() or 'german' in file.lower()):
                    src_path = os.path.join(root, file)
                    dst_path = os.path.join("assets/images", "enemy.png")
                    shutil.copy(src_path, dst_path)
                    print(f"Copied {file} to assets/images/enemy.png")
                
                # Look for boss aircraft images
                if file.lower().endswith(('.png', '.jpg')) and ('boss' in file.lower() or 'bomber' in file.lower()):
                    src_path = os.path.join(root, file)
                    dst_path = os.path.join("assets/images", "boss.png")
                    shutil.copy(src_path, dst_path)
                    print(f"Copied {file} to assets/images/boss.png")
    
    # Download explosion sound from Pixabay
    explosion_sound_url = "https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8c8a73467.mp3?filename=explosion-42132.mp3"
    download_file(explosion_sound_url, "assets/sounds/explosion.wav")
    
    # Download shooting sound
    shoot_sound_url = "https://cdn.pixabay.com/download/audio/2022/03/10/audio_270f49d29e.mp3?filename=laser-gun-81720.mp3"
    download_file(shoot_sound_url, "assets/sounds/shoot.wav")
    
    # Download hit sound
    hit_sound_url = "https://cdn.pixabay.com/download/audio/2022/03/10/audio_bf3620f48d.mp3?filename=impact-151606.mp3"
    download_file(hit_sound_url, "assets/sounds/hit.wav")
    
    # Download ally hit sound
    ally_hit_url = "https://cdn.pixabay.com/download/audio/2022/03/15/audio_db6d649e0a.mp3?filename=negative-beeps-43793.mp3"
    download_file(ally_hit_url, "assets/sounds/ally_hit.wav")
    
    # Download powerup sound
    powerup_url = "https://cdn.pixabay.com/download/audio/2022/03/10/audio_942d7d5f88.mp3?filename=collectcoin-6075.mp3"
    download_file(powerup_url, "assets/sounds/powerup.wav")
    
    # Download background music
    music_url = "https://cdn.pixabay.com/download/audio/2022/01/18/audio_d0c6ff1bab.mp3?filename=action-109593.mp3"
    download_file(music_url, "assets/music/game_music.mp3")
    
    # Download explosion animation frames (we'll use placeholders for now)
    for i in range(1, 10):
        # Create placeholder explosion frames
        with open(f"assets/images/explosion{i}.png", "w") as f:
            f.write(f"This is a placeholder for explosion frame {i}. In a real implementation, this would be a PNG file.")
    
    # Download background images
    background_url = "https://cdn.pixabay.com/download/2016/05/24/03/49/sky-1412007_1280.png"
    download_file(background_url, "assets/images/background.png")
    
    background_level1_url = "https://cdn.pixabay.com/download/2016/05/24/03/49/sky-1412007_1280.png"
    download_file(background_level1_url, "assets/images/background_level1.png")
    
    background_level2_url = "https://cdn.pixabay.com/download/2016/08/05/19/29/sunset-1573171_1280.jpg"
    download_file(background_level2_url, "assets/images/background_level2.png")
    
    # Create other placeholder images
    placeholder_images = [
        "bullet.png",
        "airfield.png",
        "ally.png",
        "building.png",
        "tank.png",
        "health_powerup.png",
        "weapon_powerup.png"
    ]
    
    for image in placeholder_images:
        if not os.path.exists(f"assets/images/{image}"):
            with open(f"assets/images/{image}", "w") as f:
                f.write(f"This is a placeholder for {image}. In a real implementation, this would be a PNG file.")
    
    # Clean up temporary files
    if os.path.exists("temp_aircraft"):
        shutil.rmtree("temp_aircraft")
        print("Cleaned up temporary files")

if __name__ == "__main__":
    download_assets()
    print("Asset download complete!")