#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Spitfire - Letecká arkádová hra
-------------------------------
Ov<PERSON><PERSON><PERSON><PERSON><PERSON>:
- <PERSON><PERSON><PERSON>: <PERSON><PERSON><PERSON>adla
- Mezerník: Střelba
- Esc: Pauza/Menu
"""

import pygame
import sys
import random
import os
import math
from pygame import mixer

# Inicializace Pygame
pygame.init()
pygame.mixer.init()

# Konstanty
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Barvy
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
GRAY = (150, 150, 150)

# Vytvoření herního okna
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Spitfire")
clock = pygame.time.Clock()

# Vytvoření adresářů pro assety, pokud neexistují
assets_dir = os.path.join(os.path.dirname(__file__), "assets")
images_dir = os.path.join(assets_dir, "images")
sounds_dir = os.path.join(assets_dir, "sounds")
music_dir = os.path.join(assets_dir, "music")

for directory in [assets_dir, images_dir, sounds_dir, music_dir]:
    if not os.path.exists(directory):
        os.makedirs(directory)

# Načtení obrázků (placeholder)
def load_image(name, scale=1):
    try:
        image = pygame.image.load(os.path.join(images_dir, name))
        if scale != 1:
            new_size = (int(image.get_width() * scale), int(image.get_height() * scale))
            image = pygame.transform.scale(image, new_size)
        return image.convert_alpha()
    except pygame.error:
        # Vytvoření placeholder obrázku, pokud soubor neexistuje
        surf = pygame.Surface((50, 50), pygame.SRCALPHA)
        pygame.draw.rect(surf, RED, surf.get_rect(), 2)
        return surf

# Načtení zvuků (placeholder)
def load_sound(name):
    try:
        return pygame.mixer.Sound(os.path.join(sounds_dir, name))
    except (pygame.error, FileNotFoundError):
        # Tichý zvuk, pokud soubor neexistuje
        print(f"Zvukový soubor {name} nebyl nalezen. Používám tichý zvuk.")
        return pygame.mixer.Sound(buffer=bytearray([]))

# Třída pro scrollující pozadí
class ScrollingBackground:
    def __init__(self, image_path, speed):
        self.image = load_image(image_path)
        if self.image.get_width() < SCREEN_WIDTH:
            self.image = pygame.transform.scale(self.image, (SCREEN_WIDTH, self.image.get_height()))
        self.speed = speed
        self.y1 = 0
        self.y2 = -self.image.get_height()
    
    def update(self):
        self.y1 += self.speed
        self.y2 += self.speed
        
        if self.y1 > SCREEN_HEIGHT:
            self.y1 = self.y2 - self.image.get_height()
        
        if self.y2 > SCREEN_HEIGHT:
            self.y2 = self.y1 - self.image.get_height()
    
    def draw(self, surface):
        surface.blit(self.image, (0, self.y1))
        surface.blit(self.image, (0, self.y2))

# Třída pro hráčovo letadlo
class Player(pygame.sprite.Sprite):
    def __init__(self):
        super().__init__()
        self.image = load_image("spitfire.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((50, 30), pygame.SRCALPHA)
            pygame.draw.polygon(self.image, BLUE, [(0, 15), (40, 0), (50, 15), (40, 30)])
        
        self.rect = self.image.get_rect()
        self.rect.centerx = SCREEN_WIDTH // 2
        self.rect.bottom = SCREEN_HEIGHT - 20
        self.speed = 5
        self.health = 100
        self.max_health = 100
        self.shoot_delay = 250  # ms
        self.last_shot = pygame.time.get_ticks()
        self.lives = 3
        self.invulnerable = False
        self.invulnerable_timer = 0
        self.score = 0
        
        # Zvuky
        self.shoot_sound = load_sound("shoot.wav")
        self.hit_sound = load_sound("hit.wav")
    
    def update(self):
        # Pohyb hráče
        keys = pygame.key.get_pressed()
        if keys[pygame.K_LEFT]:
            self.rect.x -= self.speed
        if keys[pygame.K_RIGHT]:
            self.rect.x += self.speed
        if keys[pygame.K_UP]:
            self.rect.y -= self.speed
        if keys[pygame.K_DOWN]:
            self.rect.y += self.speed
        
        # Omezení pohybu na obrazovku
        if self.rect.left < 0:
            self.rect.left = 0
        if self.rect.right > SCREEN_WIDTH:
            self.rect.right = SCREEN_WIDTH
        if self.rect.top < 0:
            self.rect.top = 0
        if self.rect.bottom > SCREEN_HEIGHT:
            self.rect.bottom = SCREEN_HEIGHT
        
        # Kontrola invulnerability
        if self.invulnerable:
            current_time = pygame.time.get_ticks()
            if current_time - self.invulnerable_timer > 3000:  # 3 sekundy
                self.invulnerable = False
    
    def shoot(self):
        now = pygame.time.get_ticks()
        if now - self.last_shot > self.shoot_delay:
            self.last_shot = now
            bullet = Bullet(self.rect.centerx, self.rect.top)
            all_sprites.add(bullet)
            bullets.add(bullet)
            self.shoot_sound.play()
    
    def hit(self, damage):
        if not self.invulnerable:
            self.health -= damage
            self.hit_sound.play()
            if self.health <= 0:
                self.lives -= 1
                if self.lives > 0:
                    self.health = self.max_health
                    self.invulnerable = True
                    self.invulnerable_timer = pygame.time.get_ticks()
                else:
                    self.kill()
                    game_over()

# Třída pro střely hráče
class Bullet(pygame.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        self.image = load_image("bullet.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((4, 10), pygame.SRCALPHA)
            pygame.draw.rect(self.image, YELLOW, (0, 0, 4, 10))
        
        self.rect = self.image.get_rect()
        self.rect.centerx = x
        self.rect.bottom = y
        self.speed = -10  # Záporná hodnota, protože střela letí nahoru
    
    def update(self):
        self.rect.y += self.speed
        # Odstranění střely, když opustí obrazovku
        if self.rect.bottom < 0:
            self.kill()

# Třída pro nepřátele
class Enemy(pygame.sprite.Sprite):
    def __init__(self, enemy_type="regular"):
        super().__init__()
        self.enemy_type = enemy_type
        
        if enemy_type == "boss":
            self.image = load_image("boss.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((100, 80), pygame.SRCALPHA)
                pygame.draw.polygon(self.image, RED, [(0, 40), (30, 0), (70, 0), (100, 40), (70, 80), (30, 80)])
            self.health = 100
            self.speed = 2
            self.score_value = 500
        else:
            self.image = load_image("enemy.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((40, 30), pygame.SRCALPHA)
                pygame.draw.polygon(self.image, RED, [(0, 15), (30, 0), (40, 15), (30, 30)])
            self.health = 20
            self.speed = 3
            self.score_value = 50
        
        self.rect = self.image.get_rect()
        self.rect.x = random.randrange(SCREEN_WIDTH - self.rect.width)
        self.rect.y = random.randrange(-100, -40)
        
        # Zvuky
        self.explosion_sound = load_sound("explosion.wav")
    
    def update(self):
        self.rect.y += self.speed
        # Pokud nepřítel opustí obrazovku, vrátí se nahoru
        if self.rect.top > SCREEN_HEIGHT:
            self.rect.x = random.randrange(SCREEN_WIDTH - self.rect.width)
            self.rect.y = random.randrange(-100, -40)
    
    def hit(self, damage):
        self.health -= damage
        if self.health <= 0:
            self.explosion_sound.play()
            explosion = Explosion(self.rect.center)
            all_sprites.add(explosion)
            self.kill()
            return self.score_value
        return 0

# Třída pro cíle na zemi
class GroundTarget(pygame.sprite.Sprite):
    def __init__(self, x, target_type="building"):
        super().__init__()
        self.target_type = target_type
        
        if target_type == "building":
            self.image = load_image("building.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((60, 40), pygame.SRCALPHA)
                pygame.draw.rect(self.image, GRAY, (0, 0, 60, 40))
                pygame.draw.polygon(self.image, RED, [(10, 0), (50, 0), (30, -20)])
            self.health = 30
            self.score_value = 100
        elif target_type == "tank":
            self.image = load_image("tank.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((40, 20), pygame.SRCALPHA)
                pygame.draw.rect(self.image, GREEN, (0, 5, 40, 15))
                pygame.draw.rect(self.image, GREEN, (10, 0, 20, 5))
            self.health = 15
            self.score_value = 75
        
        self.rect = self.image.get_rect()
        self.rect.x = x
        self.rect.bottom = SCREEN_HEIGHT - 10
        
        # Zvuky
        self.explosion_sound = load_sound("explosion.wav")
    
    def update(self):
        # Pohyb s pozadím
        self.rect.y += 1
        if self.rect.top > SCREEN_HEIGHT:
            self.kill()
    
    def hit(self, damage):
        self.health -= damage
        if self.health <= 0:
            self.explosion_sound.play()
            explosion = Explosion(self.rect.center)
            all_sprites.add(explosion)
            self.kill()
            return self.score_value
        return 0

# Třída pro spojence
class Ally(pygame.sprite.Sprite):
    def __init__(self, x):
        super().__init__()
        self.image = load_image("ally.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((20, 30), pygame.SRCALPHA)
            pygame.draw.rect(self.image, BLUE, (5, 10, 10, 20))
            pygame.draw.circle(self.image, BLUE, (10, 5), 5)
        
        self.rect = self.image.get_rect()
        self.rect.x = x
        self.rect.bottom = SCREEN_HEIGHT - 10
        self.health = 10
        
        # Zvuky
        self.hit_sound = load_sound("ally_hit.wav")
    
    def update(self):
        # Pohyb s pozadím
        self.rect.y += 1
        if self.rect.top > SCREEN_HEIGHT:
            self.kill()
    
    def hit(self, damage):
        self.health -= damage
        self.hit_sound.play()
        if self.health <= 0:
            self.kill()
            return -100  # Penalizace za ztrátu spojence
        return 0

# Třída pro výbuchy
class Explosion(pygame.sprite.Sprite):
    def __init__(self, center):
        super().__init__()
        self.image = load_image("explosion1.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((50, 50), pygame.SRCALPHA)
            pygame.draw.circle(self.image, YELLOW, (25, 25), 25)
        
        self.rect = self.image.get_rect()
        self.rect.center = center
        self.frame = 0
        self.frame_rate = 50  # ms
        self.last_update = pygame.time.get_ticks()
        
        # Animace výbuchu
        self.explosion_anim = []
        for i in range(9):
            img = load_image(f"explosion{i+1}.png")
            if img.get_width() == 50:  # Pokud je to placeholder
                img = pygame.Surface((50, 50), pygame.SRCALPHA)
                size = 25 - i * 2
                if size > 0:
                    pygame.draw.circle(img, YELLOW, (25, 25), size)
                    pygame.draw.circle(img, RED, (25, 25), size - 5 if size > 5 else 0)
            self.explosion_anim.append(img)
    
    def update(self):
        now = pygame.time.get_ticks()
        if now - self.last_update > self.frame_rate:
            self.last_update = now
            self.frame += 1
            if self.frame == len(self.explosion_anim):
                self.kill()
            else:
                center = self.rect.center
                self.image = self.explosion_anim[self.frame]
                self.rect = self.image.get_rect()
                self.rect.center = center

# Třída pro letiště (start a cíl)
class Airfield(pygame.sprite.Sprite):
    def __init__(self, is_start=True):
        super().__init__()
        self.is_start = is_start
        self.image = load_image("airfield.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((200, 50), pygame.SRCALPHA)
            pygame.draw.rect(self.image, GRAY, (0, 0, 200, 50))
            for i in range(5):
                pygame.draw.rect(self.image, WHITE, (i * 40 + 10, 20, 20, 5))
        
        self.rect = self.image.get_rect()
        if is_start:
            self.rect.centerx = SCREEN_WIDTH // 2
            self.rect.bottom = SCREEN_HEIGHT + 10  # Začíná mimo obrazovku dole
        else:
            self.rect.centerx = SCREEN_WIDTH // 2
            self.rect.top = -self.rect.height  # Končí mimo obrazovku nahoře
    
    def update(self):
        if self.is_start:
            # Startovní letiště se pohybuje nahoru
            self.rect.y -= 1
            if self.rect.bottom < 0:
                self.kill()
        else:
            # Cílové letiště se pohybuje dolů
            self.rect.y += 1
            if self.rect.top > SCREEN_HEIGHT - 100:
                # Zastavit pohyb, když je letiště viditelné
                self.rect.top = SCREEN_HEIGHT - 100

# Třída pro power-upy
class PowerUp(pygame.sprite.Sprite):
    def __init__(self, powerup_type):
        super().__init__()
        self.powerup_type = powerup_type
        
        if powerup_type == "health":
            self.image = load_image("health_powerup.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((30, 30), pygame.SRCALPHA)
                pygame.draw.circle(self.image, RED, (15, 15), 15)
                pygame.draw.rect(self.image, WHITE, (10, 5, 10, 20))
                pygame.draw.rect(self.image, WHITE, (5, 10, 20, 10))
        elif powerup_type == "weapon":
            self.image = load_image("weapon_powerup.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((30, 30), pygame.SRCALPHA)
                pygame.draw.circle(self.image, YELLOW, (15, 15), 15)
                pygame.draw.polygon(self.image, BLACK, [(15, 5), (25, 25), (15, 20), (5, 25)])
        
        self.rect = self.image.get_rect()
        self.rect.x = random.randrange(SCREEN_WIDTH - self.rect.width)
        self.rect.y = random.randrange(-100, -40)
        self.speed = 3
        
        # Zvuky
        self.pickup_sound = load_sound("powerup.wav")
    
    def update(self):
        self.rect.y += self.speed
        # Pokud power-up opustí obrazovku, odstraní se
        if self.rect.top > SCREEN_HEIGHT:
            self.kill()

# Funkce pro vykreslení textu
def draw_text(surface, text, size, x, y, color=WHITE):
    font = pygame.font.Font(None, size)
    text_surface = font.render(text, True, color)
    text_rect = text_surface.get_rect()
    text_rect.midtop = (x, y)
    surface.blit(text_surface, text_rect)

# Funkce pro vykreslení ukazatele zdraví
def draw_health_bar(surface, x, y, health, max_health, width=100, height=10):
    if health < 0:
        health = 0
    fill = (health / max_health) * width
    outline_rect = pygame.Rect(x, y, width, height)
    fill_rect = pygame.Rect(x, y, fill, height)
    pygame.draw.rect(surface, GREEN, fill_rect)
    pygame.draw.rect(surface, WHITE, outline_rect, 2)

# Funkce pro zobrazení úvodní obrazovky
def show_start_screen():
    screen.fill(BLACK)
    draw_text(screen, "SPITFIRE", 64, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4)
    draw_text(screen, "Šipky pro pohyb, Mezerník pro střelbu", 22, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
    draw_text(screen, "Stiskněte libovolnou klávesu pro start", 18, SCREEN_WIDTH // 2, SCREEN_HEIGHT * 3/4)
    pygame.display.flip()
    
    waiting = True
    while waiting:
        clock.tick(FPS)
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYUP:
                waiting = False

# Funkce pro zobrazení obrazovky konce hry
def game_over():
    screen.fill(BLACK)
    draw_text(screen, "GAME OVER", 64, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4)
    draw_text(screen, f"Skóre: {player.score}", 22, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
    draw_text(screen, "Stiskněte libovolnou klávesu pro restart", 18, SCREEN_WIDTH // 2, SCREEN_HEIGHT * 3/4)
    pygame.display.flip()
    
    waiting = True
    while waiting:
        clock.tick(FPS)
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYUP:
                waiting = False
                new_game()

# Funkce pro zobrazení obrazovky vítězství
def victory_screen():
    screen.fill(BLACK)
    draw_text(screen, "VÍTĚZSTVÍ!", 64, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4)
    draw_text(screen, f"Skóre: {player.score}", 22, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
    draw_text(screen, "Stiskněte libovolnou klávesu pro další level", 18, SCREEN_WIDTH // 2, SCREEN_HEIGHT * 3/4)
    pygame.display.flip()
    
    waiting = True
    while waiting:
        clock.tick(FPS)
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYUP:
                waiting = False
                next_level()

# Funkce pro zobrazení pauzy
def pause_game():
    paused = True
    draw_text(screen, "PAUZA", 64, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4)
    draw_text(screen, "Stiskněte P pro pokračování", 22, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
    draw_text(screen, "Stiskněte ESC pro ukončení", 18, SCREEN_WIDTH // 2, SCREEN_HEIGHT * 3/4)
    pygame.display.flip()
    
    while paused:
        clock.tick(FPS)
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_p:
                    paused = False
                if event.key == pygame.K_ESCAPE:
                    pygame.quit()
                    sys.exit()

# Funkce pro vytvoření nové hry
def new_game():
    global all_sprites, player, enemies, bullets, ground_targets, allies, powerups, level, background
    
    # Vytvoření skupin spritů
    all_sprites = pygame.sprite.Group()
    enemies = pygame.sprite.Group()
    bullets = pygame.sprite.Group()
    ground_targets = pygame.sprite.Group()
    allies = pygame.sprite.Group()
    powerups = pygame.sprite.Group()
    
    # Vytvoření hráče
    player = Player()
    all_sprites.add(player)
    
    # Nastavení úrovně
    level = 1
    
    # Vytvoření pozadí
    background = ScrollingBackground("background.png", 1)
    
    # Vytvoření startovního letiště
    start_airfield = Airfield(is_start=True)
    all_sprites.add(start_airfield)
    
    # Spuštění hudby
    try:
        pygame.mixer.music.load(os.path.join(music_dir, "game_music.mp3"))
        pygame.mixer.music.set_volume(0.5)
        pygame.mixer.music.play(-1)  # Přehrávání ve smyčce
    except pygame.error:
        pass  # Ignorovat, pokud hudba není k dispozici

# Funkce pro přechod na další úroveň
def next_level():
    global level, all_sprites, enemies, ground_targets, allies, powerups, background
    
    # Zvýšení úrovně
    level += 1
    
    # Vyčištění spritů kromě hráče
    for sprite in all_sprites:
        if sprite != player:
            sprite.kill()
    
    # Obnovení zdraví hráče
    player.health = player.max_health
    
    # Vytvoření pozadí podle úrovně
    background = ScrollingBackground(f"background_level{level}.png", 1)
    
    # Vytvoření startovního letiště
    start_airfield = Airfield(is_start=True)
    all_sprites.add(start_airfield)

# Funkce pro vytvoření bosse na konci úrovně
def spawn_boss():
    boss = Enemy(enemy_type="boss")
    all_sprites.add(boss)
    enemies.add(boss)
    
    # Vytvoření cílového letiště
    end_airfield = Airfield(is_start=False)
    all_sprites.add(end_airfield)

# Funkce pro vytvoření nepřátel
def spawn_enemies(count):
    for _ in range(count):
        enemy = Enemy()
        all_sprites.add(enemy)
        enemies.add(enemy)

# Funkce pro vytvoření pozemních cílů
def spawn_ground_targets(count):
    for _ in range(count):
        x = random.randrange(SCREEN_WIDTH - 60)
        target_type = random.choice(["building", "tank"])
        target = GroundTarget(x, target_type)
        all_sprites.add(target)
        ground_targets.add(target)

# Funkce pro vytvoření spojenců
def spawn_allies(count):
    for _ in range(count):
        x = random.randrange(SCREEN_WIDTH - 20)
        ally = Ally(x)
        all_sprites.add(ally)
        allies.add(ally)

# Funkce pro vytvoření power-upů
def spawn_powerup():
    powerup_type = random.choice(["health", "weapon"])
    powerup = PowerUp(powerup_type)
    all_sprites.add(powerup)
    powerups.add(powerup)

# Hlavní herní smyčka
def main_game_loop():
    global level, player, background
    
    # Proměnné pro sledování času
    level_time = 0
    boss_spawned = False
    victory = False
    
    # Zobrazení úvodní obrazovky
    show_start_screen()
    
    # Vytvoření nové hry
    new_game()
    
    # Hlavní herní smyčka
    running = True
    while running:
        # Udržování FPS
        clock.tick(FPS)
        
        # Zpracování událostí
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    player.shoot()
                if event.key == pygame.K_p:
                    pause_game()
                if event.key == pygame.K_ESCAPE:
                    running = False
        
        # Aktualizace
        all_sprites.update()
        background.update()
        
        # Zvýšení času úrovně
        level_time += 1
        
        # Vytvoření nepřátel a cílů podle času
        if level_time % 180 == 0 and not boss_spawned:  # Každé 3 sekundy
            spawn_enemies(level)
        
        if level_time % 300 == 0 and not boss_spawned:  # Každých 5 sekund
            spawn_ground_targets(level)
        
        if level_time % 600 == 0 and not boss_spawned:  # Každých 10 sekund
            spawn_allies(level)
        
        if level_time % 900 == 0 and not boss_spawned:  # Každých 15 sekund
            spawn_powerup()
        
        # Vytvoření bosse po určitém čase
        if level_time > 3600 and not boss_spawned:  # Po 60 sekundách
            spawn_boss()
            boss_spawned = True
        
        # Kontrola kolizí střel s nepřáteli
        hits = pygame.sprite.groupcollide(bullets, enemies, True, False)
        for bullet, enemy_list in hits.items():
            for enemy in enemy_list:
                score = enemy.hit(10)
                player.score += score
        
        # Kontrola kolizí střel s pozemními cíli
        hits = pygame.sprite.groupcollide(bullets, ground_targets, True, False)
        for bullet, target_list in hits.items():
            for target in target_list:
                score = target.hit(10)
                player.score += score
        
        # Kontrola kolizí střel se spojenci (penalizace)
        hits = pygame.sprite.groupcollide(bullets, allies, True, False)
        for bullet, ally_list in hits.items():
            for ally in ally_list:
                score = ally.hit(10)
                player.score += score
        
        # Kontrola kolizí hráče s nepřáteli
        hits = pygame.sprite.spritecollide(player, enemies, False)
        for hit in hits:
            player.hit(10)
            hit.hit(30)
        
        # Kontrola kolizí hráče s power-upy
        hits = pygame.sprite.spritecollide(player, powerups, True)
        for hit in hits:
            hit.pickup_sound.play()
            if hit.powerup_type == "health":
                player.health = min(player.health + 30, player.max_health)
            elif hit.powerup_type == "weapon":
                player.shoot_delay = max(player.shoot_delay - 50, 100)
        
        # Kontrola vítězství (všichni nepřátelé jsou zničeni a boss je zničen)
        if boss_spawned and not enemies and not victory:
            # Kontrola, zda hráč přistál na letišti
            for sprite in all_sprites:
                if isinstance(sprite, Airfield) and not sprite.is_start:
                    if pygame.sprite.collide_rect(player, sprite):
                        victory = True
                        victory_screen()
        
        # Vykreslení
        screen.fill(BLACK)
        background.draw(screen)
        all_sprites.draw(screen)
        
        # Vykreslení UI
        draw_text(screen, f"Skóre: {player.score}", 18, 70, 10)
        draw_text(screen, f"Životy: {player.lives}", 18, 70, 30)
        draw_text(screen, f"Úroveň: {level}", 18, SCREEN_WIDTH - 70, 10)
        draw_health_bar(screen, 10, 50, player.health, player.max_health)
        
        # Aktualizace obrazovky
        pygame.display.flip()
    
    # Ukončení hry
    pygame.quit()
    sys.exit()

# Spuštění hry
if __name__ == "__main__":
    main_game_loop()#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Spitfire - Letecká arkádová hra
-------------------------------
Ovládání:
- Šipky: Pohyb letadla
- Mezerník: Střelba
- Esc: Pauza/Menu
"""

import pygame
import sys
import random
import os
import math
from pygame import mixer

# Inicializace Pygame
pygame.init()
pygame.mixer.init()

# Konstanty
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Barvy
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
GRAY = (150, 150, 150)

# Vytvoření herního okna
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Spitfire")
clock = pygame.time.Clock()

# Vytvoření adresářů pro assety, pokud neexistují
assets_dir = os.path.join(os.path.dirname(__file__), "assets")
images_dir = os.path.join(assets_dir, "images")
sounds_dir = os.path.join(assets_dir, "sounds")
music_dir = os.path.join(assets_dir, "music")

for directory in [assets_dir, images_dir, sounds_dir, music_dir]:
    if not os.path.exists(directory):
        os.makedirs(directory)

# Načtení obrázků (placeholder)
def load_image(name, scale=1):
    try:
        image = pygame.image.load(os.path.join(images_dir, name))
        if scale != 1:
            new_size = (int(image.get_width() * scale), int(image.get_height() * scale))
            image = pygame.transform.scale(image, new_size)
        return image.convert_alpha()
    except pygame.error:
        # Vytvoření placeholder obrázku, pokud soubor neexistuje
        surf = pygame.Surface((50, 50), pygame.SRCALPHA)
        pygame.draw.rect(surf, RED, surf.get_rect(), 2)
        return surf

# Načtení zvuků (placeholder)
def load_sound(name):
    try:
        return pygame.mixer.Sound(os.path.join(sounds_dir, name))
    except pygame.error:
        # Tichý zvuk, pokud soubor neexistuje
        return pygame.mixer.Sound(buffer=bytearray([]))

# Třída pro scrollující pozadí
class ScrollingBackground:
    def __init__(self, image_path, speed):
        self.image = load_image(image_path)
        if self.image.get_width() < SCREEN_WIDTH:
            self.image = pygame.transform.scale(self.image, (SCREEN_WIDTH, self.image.get_height()))
        self.speed = speed
        self.y1 = 0
        self.y2 = -self.image.get_height()
    
    def update(self):
        self.y1 += self.speed
        self.y2 += self.speed
        
        if self.y1 > SCREEN_HEIGHT:
            self.y1 = self.y2 - self.image.get_height()
        
        if self.y2 > SCREEN_HEIGHT:
            self.y2 = self.y1 - self.image.get_height()
    
    def draw(self, surface):
        surface.blit(self.image, (0, self.y1))
        surface.blit(self.image, (0, self.y2))

# Třída pro hráčovo letadlo
class Player(pygame.sprite.Sprite):
    def __init__(self):
        super().__init__()
        self.image = load_image("spitfire.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((50, 30), pygame.SRCALPHA)
            pygame.draw.polygon(self.image, BLUE, [(0, 15), (40, 0), (50, 15), (40, 30)])
        
        self.rect = self.image.get_rect()
        self.rect.centerx = SCREEN_WIDTH // 2
        self.rect.bottom = SCREEN_HEIGHT - 20
        self.speed = 5
        self.health = 100
        self.max_health = 100
        self.shoot_delay = 250  # ms
        self.last_shot = pygame.time.get_ticks()
        self.lives = 3
        self.invulnerable = False
        self.invulnerable_timer = 0
        self.score = 0
        
        # Zvuky
        self.shoot_sound = load_sound("shoot.wav")
        self.hit_sound = load_sound("hit.wav")
    
    def update(self):
        # Pohyb hráče
        keys = pygame.key.get_pressed()
        if keys[pygame.K_LEFT]:
            self.rect.x -= self.speed
        if keys[pygame.K_RIGHT]:
            self.rect.x += self.speed
        if keys[pygame.K_UP]:
            self.rect.y -= self.speed
        if keys[pygame.K_DOWN]:
            self.rect.y += self.speed
        
        # Omezení pohybu na obrazovku
        if self.rect.left < 0:
            self.rect.left = 0
        if self.rect.right > SCREEN_WIDTH:
            self.rect.right = SCREEN_WIDTH
        if self.rect.top < 0:
            self.rect.top = 0
        if self.rect.bottom > SCREEN_HEIGHT:
            self.rect.bottom = SCREEN_HEIGHT
        
        # Kontrola invulnerability
        if self.invulnerable:
            current_time = pygame.time.get_ticks()
            if current_time - self.invulnerable_timer > 3000:  # 3 sekundy
                self.invulnerable = False
    
    def shoot(self):
        now = pygame.time.get_ticks()
        if now - self.last_shot > self.shoot_delay:
            self.last_shot = now
            bullet = Bullet(self.rect.centerx, self.rect.top)
            all_sprites.add(bullet)
            bullets.add(bullet)
            self.shoot_sound.play()
    
    def hit(self, damage):
        if not self.invulnerable:
            self.health -= damage
            self.hit_sound.play()
            if self.health <= 0:
                self.lives -= 1
                if self.lives > 0:
                    self.health = self.max_health
                    self.invulnerable = True
                    self.invulnerable_timer = pygame.time.get_ticks()
                else:
                    self.kill()
                    game_over()

# Třída pro střely hráče
class Bullet(pygame.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        self.image = load_image("bullet.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((4, 10), pygame.SRCALPHA)
            pygame.draw.rect(self.image, YELLOW, (0, 0, 4, 10))
        
        self.rect = self.image.get_rect()
        self.rect.centerx = x
        self.rect.bottom = y
        self.speed = -10  # Záporná hodnota, protože střela letí nahoru
    
    def update(self):
        self.rect.y += self.speed
        # Odstranění střely, když opustí obrazovku
        if self.rect.bottom < 0:
            self.kill()

# Třída pro nepřátele
class Enemy(pygame.sprite.Sprite):
    def __init__(self, enemy_type="regular"):
        super().__init__()
        self.enemy_type = enemy_type
        
        if enemy_type == "boss":
            self.image = load_image("boss.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((100, 80), pygame.SRCALPHA)
                pygame.draw.polygon(self.image, RED, [(0, 40), (30, 0), (70, 0), (100, 40), (70, 80), (30, 80)])
            self.health = 100
            self.speed = 2
            self.score_value = 500
        else:
            self.image = load_image("enemy.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((40, 30), pygame.SRCALPHA)
                pygame.draw.polygon(self.image, RED, [(0, 15), (30, 0), (40, 15), (30, 30)])
            self.health = 20
            self.speed = 3
            self.score_value = 50
        
        self.rect = self.image.get_rect()
        self.rect.x = random.randrange(SCREEN_WIDTH - self.rect.width)
        self.rect.y = random.randrange(-100, -40)
        
        # Zvuky
        self.explosion_sound = load_sound("explosion.wav")
    
    def update(self):
        self.rect.y += self.speed
        # Pokud nepřítel opustí obrazovku, vrátí se nahoru
        if self.rect.top > SCREEN_HEIGHT:
            self.rect.x = random.randrange(SCREEN_WIDTH - self.rect.width)
            self.rect.y = random.randrange(-100, -40)
    
    def hit(self, damage):
        self.health -= damage
        if self.health <= 0:
            self.explosion_sound.play()
            explosion = Explosion(self.rect.center)
            all_sprites.add(explosion)
            self.kill()
            return self.score_value
        return 0

# Třída pro cíle na zemi
class GroundTarget(pygame.sprite.Sprite):
    def __init__(self, x, target_type="building"):
        super().__init__()
        self.target_type = target_type
        
        if target_type == "building":
            self.image = load_image("building.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((60, 40), pygame.SRCALPHA)
                pygame.draw.rect(self.image, GRAY, (0, 0, 60, 40))
                pygame.draw.polygon(self.image, RED, [(10, 0), (50, 0), (30, -20)])
            self.health = 30
            self.score_value = 100
        elif target_type == "tank":
            self.image = load_image("tank.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((40, 20), pygame.SRCALPHA)
                pygame.draw.rect(self.image, GREEN, (0, 5, 40, 15))
                pygame.draw.rect(self.image, GREEN, (10, 0, 20, 5))
            self.health = 15
            self.score_value = 75
        
        self.rect = self.image.get_rect()
        self.rect.x = x
        self.rect.bottom = SCREEN_HEIGHT - 10
        
        # Zvuky
        self.explosion_sound = load_sound("explosion.wav")
    
    def update(self):
        # Pohyb s pozadím
        self.rect.y += 1
        if self.rect.top > SCREEN_HEIGHT:
            self.kill()
    
    def hit(self, damage):
        self.health -= damage
        if self.health <= 0:
            self.explosion_sound.play()
            explosion = Explosion(self.rect.center)
            all_sprites.add(explosion)
            self.kill()
            return self.score_value
        return 0

# Třída pro spojence
class Ally(pygame.sprite.Sprite):
    def __init__(self, x):
        super().__init__()
        self.image = load_image("ally.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((20, 30), pygame.SRCALPHA)
            pygame.draw.rect(self.image, BLUE, (5, 10, 10, 20))
            pygame.draw.circle(self.image, BLUE, (10, 5), 5)
        
        self.rect = self.image.get_rect()
        self.rect.x = x
        self.rect.bottom = SCREEN_HEIGHT - 10
        self.health = 10
        
        # Zvuky
        self.hit_sound = load_sound("ally_hit.wav")
    
    def update(self):
        # Pohyb s pozadím
        self.rect.y += 1
        if self.rect.top > SCREEN_HEIGHT:
            self.kill()
    
    def hit(self, damage):
        self.health -= damage
        self.hit_sound.play()
        if self.health <= 0:
            self.kill()
            return -100  # Penalizace za ztrátu spojence
        return 0

# Třída pro výbuchy
class Explosion(pygame.sprite.Sprite):
    def __init__(self, center):
        super().__init__()
        self.image = load_image("explosion1.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((50, 50), pygame.SRCALPHA)
            pygame.draw.circle(self.image, YELLOW, (25, 25), 25)
        
        self.rect = self.image.get_rect()
        self.rect.center = center
        self.frame = 0
        self.frame_rate = 50  # ms
        self.last_update = pygame.time.get_ticks()
        
        # Animace výbuchu
        self.explosion_anim = []
        for i in range(9):
            img = load_image(f"explosion{i+1}.png")
            if img.get_width() == 50:  # Pokud je to placeholder
                img = pygame.Surface((50, 50), pygame.SRCALPHA)
                size = 25 - i * 2
                if size > 0:
                    pygame.draw.circle(img, YELLOW, (25, 25), size)
                    pygame.draw.circle(img, RED, (25, 25), size - 5 if size > 5 else 0)
            self.explosion_anim.append(img)
    
    def update(self):
        now = pygame.time.get_ticks()
        if now - self.last_update > self.frame_rate:
            self.last_update = now
            self.frame += 1
            if self.frame == len(self.explosion_anim):
                self.kill()
            else:
                center = self.rect.center
                self.image = self.explosion_anim[self.frame]
                self.rect = self.image.get_rect()
                self.rect.center = center

# Třída pro letiště (start a cíl)
class Airfield(pygame.sprite.Sprite):
    def __init__(self, is_start=True):
        super().__init__()
        self.is_start = is_start
        self.image = load_image("airfield.png")
        if self.image.get_width() == 50:  # Pokud je to placeholder
            self.image = pygame.Surface((200, 50), pygame.SRCALPHA)
            pygame.draw.rect(self.image, GRAY, (0, 0, 200, 50))
            for i in range(5):
                pygame.draw.rect(self.image, WHITE, (i * 40 + 10, 20, 20, 5))
        
        self.rect = self.image.get_rect()
        if is_start:
            self.rect.centerx = SCREEN_WIDTH // 2
            self.rect.bottom = SCREEN_HEIGHT + 10  # Začíná mimo obrazovku dole
        else:
            self.rect.centerx = SCREEN_WIDTH // 2
            self.rect.top = -self.rect.height  # Končí mimo obrazovku nahoře
    
    def update(self):
        if self.is_start:
            # Startovní letiště se pohybuje nahoru
            self.rect.y -= 1
            if self.rect.bottom < 0:
                self.kill()
        else:
            # Cílové letiště se pohybuje dolů
            self.rect.y += 1
            if self.rect.top > SCREEN_HEIGHT - 100:
                # Zastavit pohyb, když je letiště viditelné
                self.rect.top = SCREEN_HEIGHT - 100

# Třída pro power-upy
class PowerUp(pygame.sprite.Sprite):
    def __init__(self, powerup_type):
        super().__init__()
        self.powerup_type = powerup_type
        
        if powerup_type == "health":
            self.image = load_image("health_powerup.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((30, 30), pygame.SRCALPHA)
                pygame.draw.circle(self.image, RED, (15, 15), 15)
                pygame.draw.rect(self.image, WHITE, (10, 5, 10, 20))
                pygame.draw.rect(self.image, WHITE, (5, 10, 20, 10))
        elif powerup_type == "weapon":
            self.image = load_image("weapon_powerup.png")
            if self.image.get_width() == 50:  # Pokud je to placeholder
                self.image = pygame.Surface((30, 30), pygame.SRCALPHA)
                pygame.draw.circle(self.image, YELLOW, (15, 15), 15)
                pygame.draw.polygon(self.image, BLACK, [(15, 5), (25, 25), (15, 20), (5, 25)])
        
        self.rect = self.image.get_rect()
        self.rect.x = random.randrange(SCREEN_WIDTH - self.rect.width)
        self.rect.y = random.randrange(-100, -40)
        self.speed = 3
        
        # Zvuky
        self.pickup_sound = load_sound("powerup.wav")
    
    def update(self):
        self.rect.y += self.speed
        # Pokud power-up opustí obrazovku, odstraní se
        if self.rect.top > SCREEN_HEIGHT:
            self.kill()

# Funkce pro vykreslení textu
def draw_text(surface, text, size, x, y, color=WHITE):
    font = pygame.font.Font(None, size)
    text_surface = font.render(text, True, color)
    text_rect = text_surface.get_rect()
    text_rect.midtop = (x, y)
    surface.blit(text_surface, text_rect)

# Funkce pro vykreslení ukazatele zdraví
def draw_health_bar(surface, x, y, health, max_health, width=100, height=10):
    if health < 0:
        health = 0
    fill = (health / max_health) * width
    outline_rect = pygame.Rect(x, y, width, height)
    fill_rect = pygame.Rect(x, y, fill, height)
    pygame.draw.rect(surface, GREEN, fill_rect)
    pygame.draw.rect(surface, WHITE, outline_rect, 2)

# Funkce pro zobrazení úvodní obrazovky
def show_start_screen():
    screen.fill(BLACK)
    draw_text(screen, "SPITFIRE", 64, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4)
    draw_text(screen, "Šipky pro pohyb, Mezerník pro střelbu", 22, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
    draw_text(screen, "Stiskněte libovolnou klávesu pro start", 18, SCREEN_WIDTH // 2, SCREEN_HEIGHT * 3/4)
    pygame.display.flip()
    
    waiting = True
    while waiting:
        clock.tick(FPS)
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYUP:
                waiting = False

# Funkce pro zobrazení obrazovky konce hry
def game_over():
    screen.fill(BLACK)
    draw_text(screen, "GAME OVER", 64, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4)
    draw_text(screen, f"Skóre: {player.score}", 22, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
    draw_text(screen, "Stiskněte libovolnou klávesu pro restart", 18, SCREEN_WIDTH // 2, SCREEN_HEIGHT * 3/4)
    pygame.display.flip()
    
    waiting = True
    while waiting:
        clock.tick(FPS)
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYUP:
                waiting = False
                new_game()

# Funkce pro zobrazení obrazovky vítězství
def victory_screen():
    screen.fill(BLACK)
    draw_text(screen, "VÍTĚZSTVÍ!", 64, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4)
    draw_text(screen, f"Skóre: {player.score}", 22, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
    draw_text(screen, "Stiskněte libovolnou klávesu pro další level", 18, SCREEN_WIDTH // 2, SCREEN_HEIGHT * 3/4)
    pygame.display.flip()
    
    waiting = True
    while waiting:
        clock.tick(FPS)
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYUP:
                waiting = False
                next_level()

# Funkce pro zobrazení pauzy
def pause_game():
    paused = True
    draw_text(screen, "PAUZA", 64, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4)
    draw_text(screen, "Stiskněte P pro pokračování", 22, SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
    draw_text(screen, "Stiskněte ESC pro ukončení", 18, SCREEN_WIDTH // 2, SCREEN_HEIGHT * 3/4)
    pygame.display.flip()
    
    while paused:
        clock.tick(FPS)
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_p:
                    paused = False
                if event.key == pygame.K_ESCAPE:
                    pygame.quit()
                    sys.exit()

# Funkce pro vytvoření nové hry
def new_game():
    global all_sprites, player, enemies, bullets, ground_targets, allies, powerups, level, background
    
    # Vytvoření skupin spritů
    all_sprites = pygame.sprite.Group()
    enemies = pygame.sprite.Group()
    bullets = pygame.sprite.Group()
    ground_targets = pygame.sprite.Group()
    allies = pygame.sprite.Group()
    powerups = pygame.sprite.Group()
    
    # Vytvoření hráče
    player = Player()
    all_sprites.add(player)
    
    # Nastavení úrovně
    level = 1
    
    # Vytvoření pozadí
    background = ScrollingBackground("background.png", 1)
    
    # Vytvoření startovního letiště
    start_airfield = Airfield(is_start=True)
    all_sprites.add(start_airfield)
    
    # Spuštění hudby
    try:
        pygame.mixer.music.load(os.path.join(music_dir, "game_music.mp3"))
        pygame.mixer.music.set_volume(0.5)
        pygame.mixer.music.play(-1)  # Přehrávání ve smyčce
    except pygame.error:
        pass  # Ignorovat, pokud hudba není k dispozici

# Funkce pro přechod na další úroveň
def next_level():
    global level, all_sprites, enemies, ground_targets, allies, powerups, background
    
    # Zvýšení úrovně
    level += 1
    
    # Vyčištění spritů kromě hráče
    for sprite in all_sprites:
        if sprite != player:
            sprite.kill()
    
    # Obnovení zdraví hráče
    player.health = player.max_health
    
    # Vytvoření pozadí podle úrovně
    background = ScrollingBackground(f"background_level{level}.png", 1)
    
    # Vytvoření startovního letiště
    start_airfield = Airfield(is_start=True)
    all_sprites.add(start_airfield)

# Funkce pro vytvoření bosse na konci úrovně
def spawn_boss():
    boss = Enemy(enemy_type="boss")
    all_sprites.add(boss)
    enemies.add(boss)
    
    # Vytvoření cílového letiště
    end_airfield = Airfield(is_start=False)
    all_sprites.add(end_airfield)

# Funkce pro vytvoření nepřátel
def spawn_enemies(count):
    for _ in range(count):
        enemy = Enemy()
        all_sprites.add(enemy)
        enemies.add(enemy)

# Funkce pro vytvoření pozemních cílů
def spawn_ground_targets(count):
    for _ in range(count):
        x = random.randrange(SCREEN_WIDTH - 60)
        target_type = random.choice(["building", "tank"])
        target = GroundTarget(x, target_type)
        all_sprites.add(target)
        ground_targets.add(target)

# Funkce pro vytvoření spojenců
def spawn_allies(count):
    for _ in range(count):
        x = random.randrange(SCREEN_WIDTH - 20)
        ally = Ally(x)
        all_sprites.add(ally)
        allies.add(ally)

# Funkce pro vytvoření power-upů
def spawn_powerup():
    powerup_type = random.choice(["health", "weapon"])
    powerup = PowerUp(powerup_type)
    all_sprites.add(powerup)
    powerups.add(powerup)

# Hlavní herní smyčka
def main_game_loop():
    global level, player, background
    
    # Proměnné pro sledování času
    level_time = 0
    boss_spawned = False
    victory = False
    
    # Zobrazení úvodní obrazovky
    show_start_screen()
    
    # Vytvoření nové hry
    new_game()
    
    # Hlavní herní smyčka
    running = True
    while running:
        # Udržování FPS
        clock.tick(FPS)
        
        # Zpracování událostí
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    player.shoot()
                if event.key == pygame.K_p:
                    pause_game()
                if event.key == pygame.K_ESCAPE:
                    running = False
        
        # Aktualizace
        all_sprites.update()
        background.update()
        
        # Zvýšení času úrovně
        level_time += 1
        
        # Vytvoření nepřátel a cílů podle času
        if level_time % 180 == 0 and not boss_spawned:  # Každé 3 sekundy
            spawn_enemies(level)
        
        if level_time % 300 == 0 and not boss_spawned:  # Každých 5 sekund
            spawn_ground_targets(level)
        
        if level_time % 600 == 0 and not boss_spawned:  # Každých 10 sekund
            spawn_allies(level)
        
        if level_time % 900 == 0 and not boss_spawned:  # Každých 15 sekund
            spawn_powerup()
        
        # Vytvoření bosse po určitém čase
        if level_time > 3600 and not boss_spawned:  # Po 60 sekundách
            spawn_boss()
            boss_spawned = True
        
        # Kontrola kolizí střel s nepřáteli
        hits = pygame.sprite.groupcollide(bullets, enemies, True, False)
        for bullet, enemy_list in hits.items():
            for enemy in enemy_list:
                score = enemy.hit(10)
                player.score += score
        
        # Kontrola kolizí střel s pozemními cíli
        hits = pygame.sprite.groupcollide(bullets, ground_targets, True, False)
        for bullet, target_list in hits.items():
            for target in target_list:
                score = target.hit(10)
                player.score += score
        
        # Kontrola kolizí střel se spojenci (penalizace)
        hits = pygame.sprite.groupcollide(bullets, allies, True, False)
        for bullet, ally_list in hits.items():
            for ally in ally_list:
                score = ally.hit(10)
                player.score += score
        
        # Kontrola kolizí hráče s nepřáteli
        hits = pygame.sprite.spritecollide(player, enemies, False)
        for hit in hits:
            player.hit(10)
            hit.hit(30)
        
        # Kontrola kolizí hráče s power-upy
        hits = pygame.sprite.spritecollide(player, powerups, True)
        for hit in hits:
            hit.pickup_sound.play()
            if hit.powerup_type == "health":
                player.health = min(player.health + 30, player.max_health)
            elif hit.powerup_type == "weapon":
                player.shoot_delay = max(player.shoot_delay - 50, 100)
        
        # Kontrola vítězství (všichni nepřátelé jsou zničeni a boss je zničen)
        if boss_spawned and not enemies and not victory:
            # Kontrola, zda hráč přistál na letišti
            for sprite in all_sprites:
                if isinstance(sprite, Airfield) and not sprite.is_start:
                    if pygame.sprite.collide_rect(player, sprite):
                        victory = True
                        victory_screen()
        
        # Vykreslení
        screen.fill(BLACK)
        background.draw(screen)
        all_sprites.draw(screen)
        
        # Vykreslení UI
        draw_text(screen, f"Skóre: {player.score}", 18, 70, 10)
        draw_text(screen, f"Životy: {player.lives}", 18, 70, 30)
        draw_text(screen, f"Úroveň: {level}", 18, SCREEN_WIDTH - 70, 10)
        draw_health_bar(screen, 10, 50, player.health, player.max_health)
        
        # Aktualizace obrazovky
        pygame.display.flip()
    
    # Ukončení hry
    pygame.quit()
    sys.exit()

# Spuštění hry
if __name__ == "__main__":
    main_game_loop()