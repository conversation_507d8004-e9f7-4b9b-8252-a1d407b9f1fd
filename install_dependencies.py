#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Spitfire Game Dependencies Installer
----------------------------------
This script installs all dependencies needed for the Spitfire game.
"""

import sys
import subprocess
import os

def install_package(package):
    """Install a Python package using pip."""
    print(f"Installing {package}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"{package} installed successfully!")
        return True
    except Exception as e:
        print(f"Failed to install {package}: {e}")
        return False

def main():
    """Install all required dependencies."""
    print("Installing Spitfire game dependencies...")
    
    # Required packages
    packages = [
        "pygame>=2.0.0",
        "numpy>=1.19.0"
    ]
    
    # Install each package
    success = True
    for package in packages:
        if not install_package(package):
            success = False
    
    if success:
        print("\nAll dependencies installed successfully!")
        print("You can now run the game with: python setup_and_run.py")
    else:
        print("\nSome dependencies could not be installed.")
        print("Please install them manually and then run: python setup_and_run.py")

if __name__ == "__main__":
    main()