#!/usr/bin/env python3
"""
Vylepšení a opravy pro hru Spitfire
Obsahuje nové systémy a opravy chyb
"""

import pygame
import json
import os
import math
import random
from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional

# Nové konstanty
class GameState(Enum):
    MENU = "menu"
    PLAYING = "playing"
    PAUSED = "paused"
    GAME_OVER = "game_over"
    SETTINGS = "settings"
    TUTORIAL = "tutorial"
    ACHIEVEMENTS = "achievements"

class Difficulty(Enum):
    EASY = "easy"
    NORMAL = "normal"
    HARD = "hard"
    NIGHTMARE = "nightmare"

@dataclass
class GameConfig:
    """Konfigurace hry"""
    screen_width: int = 800
    screen_height: int = 600
    fps: int = 60
    master_volume: float = 0.7
    music_volume: float = 0.5
    sfx_volume: float = 0.8
    difficulty: Difficulty = Difficulty.NORMAL
    fullscreen: bool = False
    vsync: bool = True
    show_fps: bool = False
    auto_save: bool = True

class GameManager:
    """Hlavní správce hry - opravuje globální proměnné"""
    
    def __init__(self):
        self.config = GameConfig()
        self.state = GameState.MENU
        self.previous_state = None
        self.running = True
        
        # Herní objekty
        self.player = None
        self.all_sprites = pygame.sprite.Group()
        self.bullets = pygame.sprite.Group()
        self.enemy_bullets = pygame.sprite.Group()
        self.enemies = pygame.sprite.Group()
        self.ground_targets = pygame.sprite.Group()
        self.allies = pygame.sprite.Group()
        self.powerups = pygame.sprite.Group()
        self.particles = pygame.sprite.Group()
        
        # Herní statistiky
        self.score = 0
        self.level = 1
        self.lives = 3
        self.time_played = 0
        self.enemies_killed = 0
        self.accuracy = 0.0
        self.shots_fired = 0
        self.shots_hit = 0
        
        # Systémy
        self.save_system = SaveSystem()
        self.achievement_system = AchievementSystem()
        self.performance_monitor = PerformanceMonitor()
        
    def change_state(self, new_state: GameState):
        """Bezpečná změna stavu hry"""
        self.previous_state = self.state
        self.state = new_state
        print(f"Game state changed: {self.previous_state.value} -> {new_state.value}")

class SaveSystem:
    """Systém pro ukládání a načítání hry"""
    
    def __init__(self):
        self.save_dir = "saves"
        self.config_file = "config.json"
        self.stats_file = "stats.json"
        self.ensure_save_directory()
    
    def ensure_save_directory(self):
        """Vytvoří adresář pro uložené hry"""
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
    
    def save_config(self, config: GameConfig):
        """Uloží konfiguraci hry"""
        try:
            config_data = {
                'screen_width': config.screen_width,
                'screen_height': config.screen_height,
                'fps': config.fps,
                'master_volume': config.master_volume,
                'music_volume': config.music_volume,
                'sfx_volume': config.sfx_volume,
                'difficulty': config.difficulty.value,
                'fullscreen': config.fullscreen,
                'vsync': config.vsync,
                'show_fps': config.show_fps,
                'auto_save': config.auto_save
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            print("Konfigurace uložena")
            
        except Exception as e:
            print(f"Chyba při ukládání konfigurace: {e}")
    
    def load_config(self) -> GameConfig:
        """Načte konfiguraci hry"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    data = json.load(f)
                
                config = GameConfig()
                config.screen_width = data.get('screen_width', 800)
                config.screen_height = data.get('screen_height', 600)
                config.fps = data.get('fps', 60)
                config.master_volume = data.get('master_volume', 0.7)
                config.music_volume = data.get('music_volume', 0.5)
                config.sfx_volume = data.get('sfx_volume', 0.8)
                config.difficulty = Difficulty(data.get('difficulty', 'normal'))
                config.fullscreen = data.get('fullscreen', False)
                config.vsync = data.get('vsync', True)
                config.show_fps = data.get('show_fps', False)
                config.auto_save = data.get('auto_save', True)
                
                print("Konfigurace načtena")
                return config
                
        except Exception as e:
            print(f"Chyba při načítání konfigurace: {e}")
        
        return GameConfig()  # Výchozí konfigurace
    
    def save_game_state(self, game_manager):
        """Uloží stav hry"""
        try:
            save_data = {
                'score': game_manager.score,
                'level': game_manager.level,
                'lives': game_manager.lives,
                'time_played': game_manager.time_played,
                'enemies_killed': game_manager.enemies_killed,
                'shots_fired': game_manager.shots_fired,
                'shots_hit': game_manager.shots_hit,
                'player_health': game_manager.player.health if game_manager.player else 100,
                'player_weapon_level': game_manager.player.weapon_level if game_manager.player else 1,
                'timestamp': pygame.time.get_ticks()
            }
            
            save_file = os.path.join(self.save_dir, "quicksave.json")
            with open(save_file, 'w') as f:
                json.dump(save_data, f, indent=2)
            print("Hra uložena")
            
        except Exception as e:
            print(f"Chyba při ukládání hry: {e}")

class AchievementSystem:
    """Systém achievementů"""
    
    def __init__(self):
        self.achievements = {
            'first_kill': {'name': 'První zabití', 'description': 'Zničte prvního nepřítele', 'unlocked': False},
            'ace_pilot': {'name': 'Eso pilot', 'description': 'Zničte 50 nepřátel', 'unlocked': False},
            'sharpshooter': {'name': 'Ostrostřelec', 'description': 'Dosáhněte 90% přesnosti', 'unlocked': False},
            'survivor': {'name': 'Přeživší', 'description': 'Dokončete 10 úrovní', 'unlocked': False},
            'speed_demon': {'name': 'Rychlík', 'description': 'Dokončete úroveň pod 2 minuty', 'unlocked': False},
            'collector': {'name': 'Sběratel', 'description': 'Seberte 25 power-upů', 'unlocked': False},
            'untouchable': {'name': 'Nedotknutelný', 'description': 'Dokončete úroveň bez poškození', 'unlocked': False}
        }
        self.load_achievements()
    
    def check_achievement(self, achievement_id: str, condition: bool):
        """Kontroluje a odemyká achievement"""
        if achievement_id in self.achievements and not self.achievements[achievement_id]['unlocked']:
            if condition:
                self.achievements[achievement_id]['unlocked'] = True
                self.show_achievement_notification(achievement_id)
                self.save_achievements()
    
    def show_achievement_notification(self, achievement_id: str):
        """Zobrazí notifikaci o odemčeném achievementu"""
        achievement = self.achievements[achievement_id]
        print(f"🏆 Achievement odemčen: {achievement['name']}")
        # Zde by bylo zobrazení na obrazovce
    
    def save_achievements(self):
        """Uloží achievementy"""
        try:
            with open("achievements.json", 'w') as f:
                json.dump(self.achievements, f, indent=2)
        except Exception as e:
            print(f"Chyba při ukládání achievementů: {e}")
    
    def load_achievements(self):
        """Načte achievementy"""
        try:
            if os.path.exists("achievements.json"):
                with open("achievements.json", 'r') as f:
                    saved_achievements = json.load(f)
                    for aid, data in saved_achievements.items():
                        if aid in self.achievements:
                            self.achievements[aid]['unlocked'] = data.get('unlocked', False)
        except Exception as e:
            print(f"Chyba při načítání achievementů: {e}")

class PerformanceMonitor:
    """Monitor výkonu hry"""
    
    def __init__(self):
        self.fps_history = []
        self.frame_times = []
        self.max_history = 60  # Posledních 60 snímků
        self.last_frame_time = pygame.time.get_ticks()
    
    def update(self, clock):
        """Aktualizuje statistiky výkonu"""
        current_time = pygame.time.get_ticks()
        frame_time = current_time - self.last_frame_time
        self.last_frame_time = current_time
        
        # Přidání do historie
        self.fps_history.append(clock.get_fps())
        self.frame_times.append(frame_time)
        
        # Omezení velikosti historie
        if len(self.fps_history) > self.max_history:
            self.fps_history.pop(0)
        if len(self.frame_times) > self.max_history:
            self.frame_times.pop(0)
    
    def get_average_fps(self) -> float:
        """Vrátí průměrné FPS"""
        if self.fps_history:
            return sum(self.fps_history) / len(self.fps_history)
        return 0.0
    
    def get_frame_time_ms(self) -> float:
        """Vrátí průměrný čas snímku v ms"""
        if self.frame_times:
            return sum(self.frame_times) / len(self.frame_times)
        return 0.0
    
    def is_performance_good(self) -> bool:
        """Kontroluje, zda je výkon dobrý"""
        avg_fps = self.get_average_fps()
        return avg_fps >= 55  # Považujeme za dobré, pokud je FPS >= 55

# Optimalizované objekty
class ObjectPool:
    """Pool objektů pro lepší výkon"""
    
    def __init__(self, object_class, initial_size=50):
        self.object_class = object_class
        self.available = []
        self.in_use = []
        
        # Vytvoření počátečních objektů
        for _ in range(initial_size):
            obj = object_class()
            self.available.append(obj)
    
    def get_object(self):
        """Získá objekt z poolu"""
        if self.available:
            obj = self.available.pop()
            self.in_use.append(obj)
            return obj
        else:
            # Vytvoří nový, pokud pool je prázdný
            obj = self.object_class()
            self.in_use.append(obj)
            return obj
    
    def return_object(self, obj):
        """Vrátí objekt do poolu"""
        if obj in self.in_use:
            self.in_use.remove(obj)
            obj.reset()  # Objekt musí mít reset() metodu
            self.available.append(obj)

# Vylepšené efekty
class ParticleSystem:
    """Systém částic pro efekty"""
    
    def __init__(self):
        self.particles = []
    
    def add_explosion(self, x, y, color=(255, 100, 0)):
        """Přidá efekt exploze"""
        for _ in range(20):
            particle = {
                'x': x,
                'y': y,
                'vx': random.uniform(-5, 5),
                'vy': random.uniform(-5, 5),
                'life': 60,
                'max_life': 60,
                'color': color,
                'size': random.randint(2, 6)
            }
            self.particles.append(particle)
    
    def update(self):
        """Aktualizuje všechny částice"""
        for particle in self.particles[:]:  # Kopie seznamu
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            particle['life'] -= 1
            particle['vy'] += 0.1  # Gravitace
            
            if particle['life'] <= 0:
                self.particles.remove(particle)
    
    def draw(self, surface):
        """Vykreslí všechny částice"""
        for particle in self.particles:
            alpha = particle['life'] / particle['max_life']
            color = (*particle['color'], int(255 * alpha))
            size = int(particle['size'] * alpha)
            
            if size > 0:
                pygame.draw.circle(surface, particle['color'][:3], 
                                 (int(particle['x']), int(particle['y'])), size)

def create_game_manager():
    """Factory funkce pro vytvoření game manageru"""
    return GameManager()
