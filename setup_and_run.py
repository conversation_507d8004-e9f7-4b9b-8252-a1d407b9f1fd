#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Spitfire Game Setup and Run
--------------------------
This script sets up and runs the Spitfire game.
"""

import os
import sys
import subprocess
import time

def check_dependencies():
    """Check if all required dependencies are installed."""
    try:
        import pygame
        print(f"Pygame is installed. Version: {pygame.version.ver}")
        return True
    except ImportError:
        print("Pygame is not installed. Installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pygame"])
            print("Pygame installed successfully!")
            return True
        except Exception as e:
            print(f"Failed to install Pygame: {e}")
            print("Please install Pygame manually with: pip install pygame")
            return False

def run_script(script_name):
    """Run a Python script and return its exit code."""
    print(f"Running {script_name}...")
    try:
        result = subprocess.run([sys.executable, script_name], check=True)
        print(f"{script_name} completed successfully!")
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"Error running {script_name}: {e}")
        return e.returncode

def main():
    """Main function to set up and run the game."""
    print("Setting up Spitfire game...")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Skip download attempt and directly generate assets locally
    print("\nGenerating graphics locally...")
    generate_result = run_script("generate_assets.py")
    if generate_result != 0:
        print("Failed to generate graphics. Exiting.")
        sys.exit(1)

    # Generate sound effects
    print("\nGenerating sound effects locally...")
    try:
        import numpy
        sound_result = run_script("generate_sounds.py")
        if sound_result != 0:
            print("Failed to generate sounds, but continuing without them.")
    except ImportError:
        print("NumPy not installed. Installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "numpy"])
            print("NumPy installed successfully!")
            sound_result = run_script("generate_sounds.py")
            if sound_result != 0:
                print("Failed to generate sounds, but continuing without them.")
        except Exception as e:
            print(f"Failed to install NumPy: {e}")
            print("Game will run without sound effects.")
    
    # Update the game
    print("\nUpdating game...")
    update_result = run_script("update_game.py")
    if update_result != 0:
        print("Failed to update game. Exiting.")
        sys.exit(1)
    
    # Run the game
    print("\nStarting Spitfire game...")
    time.sleep(1)  # Short pause for dramatic effect
    run_script("spitfire_game.py")

if __name__ == "__main__":
    main()