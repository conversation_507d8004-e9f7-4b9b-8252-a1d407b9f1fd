#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Spitfire Game Launcher
----------------------
This script launches the Spitfire game and handles any initialization needed.
"""

import os
import sys
import subprocess

def check_dependencies():
    """Check if all required dependencies are installed."""
    try:
        import pygame
        print("Pygame is installed. Version:", pygame.version.ver)
        return True
    except ImportError:
        print("Pygame is not installed. Installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pygame"])
            print("Pygame installed successfully!")
            return True
        except Exception as e:
            print(f"Failed to install Pygame: {e}")
            print("Please install Pygame manually with: pip install pygame")
            return False

def main():
    """Main function to launch the game."""
    # Check if we're running the correct Python version
    if sys.version_info < (3, 6):
        print("This game requires Python 3.6 or newer.")
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Launch the game
    print("Starting Spitfire game...")
    game_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "spitfire_game.py")
    
    if os.path.exists(game_path):
        if sys.platform.startswith('win'):
            os.system(f'python "{game_path}"')
        else:
            os.system(f'python3 "{game_path}"')
    else:
        print(f"Error: Game file not found at {game_path}")
        sys.exit(1)

if __name__ == "__main__":
    main()